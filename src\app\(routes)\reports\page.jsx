"use client";
import React, { useState, useEffect } from "react";
import {
  BuildingOffice2Icon,
  EnvelopeIcon,
  PhoneIcon,
} from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import { MagnifyingGlassIcon } from "@heroicons/react/20/solid";
import Select from "react-select";
import Cookies from "js-cookie";
import { Fragment } from "react";
import { Popover, Transition } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import { convertDateIntoIndianFormat } from "@/helpers/dateHelpers";
import { AlertModal } from "@/components/AlertModal";
import Pagination from "@/components/Pagination/Pagination";
import API from "@/components/API";
import moment from "moment-timezone";
import InvoiceTemplate from "@/components/Invoice/InvoiceTemplate";
const classOptions = [
  { value: "class", label: "Class" },
  { value: "report", label: "Course" },
  { value: "", label: "All" },
];

const statusOptions = [
  { value: "paid", label: "Paid" },
  { value: "unpaid", label: "Unpaid" },
  { value: "", label: "All" },
];

export default function Reports() {
  const router = useRouter();
  const today = moment().tz("Asia/Kolkata").format("YYYY-MM-DD");
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  const [searchLoading, setSearchLoading] = useState(false);
  const [classFilter, setClassFilter] = useState();
  const [statusFilter, setStatusFilter] = useState();
  const [startDate, setStartDate] = useState(today);
  const [endDate, setEndDate] = useState(today);
  const [minEndDate, setMinEndDate] = useState(today);
  const [maxEndDate, setMaxEndDate] = useState(today);

  const [searchValue, setSearchValue] = useState("");
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedPage, setSelectedPage] = useState(1);
  const [showConfirm, setShowConfirm] = useState(false);
  const [currentReport, setCurrentReport] = useState(null);
  const [token, setToken] = useState("");
  const [singleReport, setSingleReport] = useState({});
  const [singleReportIndex, setSingleReportIndex] = useState({});

  const [open, setOpen] = useState(false);

  const getSearchResult = async ({
    search = "",
    clas = null,
    status = null,
    start = null,
    end = null,
  } = {}) => {
    try {
      // console.log(classFilter, statusFilter, "pp");
      const obj = {
        status: status ? status?.value : statusFilter?.value,
        startDate: start ? start : startDate,
        endDate: end ? end : endDate,
        page: selectedPage,
      };
      // console.log("running", obj);
      setSearchLoading(true);
      setSearchValue(search);
      setStatusFilter(status);
      setClassFilter(clas);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(obj),
      };

      const response = await fetch(`/api/reports`, requestOptions);

      const result = await response.json();
      // console.log(result, "result here");
      setLoading(false);

      if (!result.error) {
        setCourses(result?.data?.report);
        setToken(result?.token);
        setSearchLoading(false);
        setTotalResults(Number(result?.data?.totalResults));
        setTotalPages(Number(result?.data?.totalPages));
        setCurrentPage(Number(result?.data?.currentPage));
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
      setSearchLoading(false);
    }
  };
  useEffect(() => {
    getSearchResult();
  }, [selectedPage, setSelectedPage, startDate, endDate]);

  useEffect(() => {
    const start = moment(startDate).tz("Asia/Kolkata").startOf("day");
    const minEnd = start.clone();
    const maxEnd = moment.min(start.clone().add(7, "days"), moment(today));
    setMinEndDate(minEnd.format("YYYY-MM-DD"));
    setMaxEndDate(maxEnd.format("YYYY-MM-DD"));
    if (moment(endDate).isBefore(minEnd) || moment(endDate).isAfter(maxEnd)) {
      setEndDate(minEnd.format("YYYY-MM-DD"));
    }
  }, [startDate]);
  const handleToggleStatus = (report) => {
    setCurrentReport(report);
    setShowConfirm(true);
  };

  const handleConfirmToggle = async () => {
    try {
      const updatedStatus = currentReport.status === "paid" ? "unpaid" : "paid";

      const response = await fetch(
        `${API}/api/booking/markPaymentStatusPaid?classId=${currentReport.classId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const result = await response.json();
      if (!result.error) {
        getSearchResult();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setShowConfirm(false);
      setCurrentReport(null);
    }
  };

  const handleDownload = async ({
    search = "",
    clas = null,
    status = null,
    start = null,
    end = null,
  } = {}) => {
    try {
      const obj = {
        status: status ? status?.value : statusFilter?.value,
        startDate: start ? start : startDate,
        endDate: end ? end : endDate,
        page: selectedPage,
      };

      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(obj),
      };

      const result = await fetch(`/api/download-report`, requestOptions);
      const response = await result.json();

      // console.log(response, "excel file");

      // Create a link to download the CSV file
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "report.csv");
      document.body.appendChild(link);
      link.click();
    } catch (error) {
      console.error(`Download CSV Error: ${error.message}`);
    }
  };
  return (
    <>
      {open && (
        <InvoiceTemplate
          open={open}
          setOpen={setOpen}
          singleReport={singleReport}
          singleReportIndex={singleReportIndex}
        />
      )}
      <title>Coach Reports - complete payment related data</title>
      {loading ? (
        <div className="flex items-center justify-center h-screen">
          <div
            className="inline-block h-20 w-20 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
            role="status"
          >
            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
              Loading...
            </span>
          </div>
        </div>
      ) : (
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8 min-h-[500px]">
                <header className="mb-2">
                  <div className="mt-2 flex rounded-md shadow-sm justify-between">
                    <div className="flex justify-end">
                      <Select
                        options={statusOptions}
                        onChange={(value) => {
                          setStatusFilter(value);
                          setSelectedPage(1);
                          getSearchResult({
                            search: searchValue,
                            status: value,
                            clas: classFilter,
                            start: startDate,
                            end: endDate,
                          });
                        }}
                        isSearchable={false}
                        placeholder={"Status Type"}
                      />
                      <Popover className="relative p-2 border py-1.5 border-grey-600 bg-white rounded-none rounded-1-md hover:bg-slate-100 h-9.5">
                        <Popover.Button className="inline-flex items-center gap-x-1 text-sm font-semibold leading-6 text-grey-300">
                          <span>Date Range</span>
                          <ChevronDownIcon
                            className="h-5 w-5"
                            aria-hidden="true"
                          />
                        </Popover.Button>

                        <Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <Popover.Panel className="absolute left-1/2 z-10 mt-5 flex w-screen max-w-max -translate-x-1/2 px-4">
                            <div className="w-screen max-w-sm flex-auto rounded-3xl bg-white p-4 text-sm leading-6 shadow-lg ring-1 ring-gray-900/5">
                              <div>
                                <div>
                                  <label
                                    htmlFor="start_date"
                                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                  >
                                    Start date
                                  </label>
                                  <div className="mt-2">
                                    <input
                                      type="date"
                                      name="start_date"
                                      value={startDate}
                                      id="start_date"
                                      max={today}
                                      onChange={(e) => {
                                        setSelectedPage(1);
                                        setStartDate(e.target.value);
                                        getSearchResult({
                                          search: searchValue,
                                          status: statusFilter,
                                          clas: classFilter,
                                          start: e.target.value,
                                          end: endDate,
                                        });
                                      }}
                                      className="block p-3 w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    />
                                  </div>
                                </div>

                                <div>
                                  <label
                                    htmlFor="end_date"
                                    className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                  >
                                    End date
                                  </label>
                                  <div className="mt-2">
                                    <input
                                      type="date"
                                      name="end_date"
                                      value={endDate}
                                      id="end_date"
                                      min={minEndDate}
                                      max={maxEndDate}
                                      onChange={(e) => {
                                        // console.log(e.target.value, "iiii");
                                        setSelectedPage(1);
                                        setEndDate(e.target.value);
                                        getSearchResult({
                                          search: searchValue,
                                          status: statusFilter,
                                          clas: classFilter,
                                          start: startDate,
                                          end: e.target.value,
                                        });
                                      }}
                                      className="block p-3 w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                      // placeholder="<EMAIL>"
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Popover.Panel>
                        </Transition>
                      </Popover>
                    </div>

                    <div>
                      <button
                        className="text-blue-500"
                        onClick={handleDownload}
                      >
                        Download Report
                      </button>
                    </div>
                  </div>
                </header>

                {/* Table */}
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Booking Id
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Date
                        </th>

                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Course Name
                        </th>
                        {/* <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Coach Name
                        </th> */}
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          GST (if applicable)
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Course fees(with GST)
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Refunded Amount (if any)
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          TDS
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Amount Received
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Status
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Coach attendance
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Player attendance
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Invoice
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {courses &&
                        courses.length > 0 &&
                        courses?.map((report, idx) => (
                          <tr className="cursor-pointer" key={report.classId}>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report?.bookingId}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report?.date.split("T")[0]}
                            </td>

                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report?.courseName
                                .split(" ")
                                .slice(0, 4)
                                .join(" ")}
                            </td>
                            {/* <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report?.coachName}
                            </td> */}
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report.hasGst
                                ? report?.classFees * (18 / (100 + 18))
                                : 0}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report?.classFees.toFixed(2)}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report?.refundAmount?.toFixed(2)}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report?.tds?.toFixed(2)}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              {report?.amountReceived?.toFixed(2)}
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              <label className="inline-flex items-center">
                                {report.paymentStatus == "paid" ? (
                                  <span className="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                                    Paid
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">
                                    Unpaid
                                  </span>
                                )}
                              </label>
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              <label className="inline-flex items-center">
                                {report.coachAttendance == "present" ? (
                                  <span className="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                                    Present
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">
                                    NA
                                  </span>
                                )}
                              </label>
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              <label className="inline-flex items-center">
                                {report.playerAttendance == "present" ? (
                                  <span className="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                                    Present
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">
                                    NA
                                  </span>
                                )}
                              </label>
                            </td>
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                              <button
                                className="hover:bg-blue-100 text-blue-500 font-bold rounded inline-flex items-center"
                                onClick={() => {
                                  setOpen(!open);
                                  setSingleReport(report);
                                  setSingleReportIndex(idx);
                                }}
                              >
                                <svg
                                  class="fill-current w-4 h-4 mr-2"
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 20 20"
                                >
                                  <path d="M13 8V2H7v6H2l8 8 8-8h-5zM0 18h20v2H0v-2z" />
                                </svg>
                                <span>Invoice</span>
                              </button>
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
                {courses && courses.length > 0 && (
                  <Pagination
                    totalResults={totalResults}
                    currentPage={currentPage}
                    totalPages={totalPages}
                    setSelectedPage={setSelectedPage}
                    selectedPage={selectedPage}
                  />
                )}
                {!(courses && courses.length > 0) && (
                  <span className="flex justify-center mt-2">
                    No result found.
                  </span>
                )}
                {showConfirm && (
                  <AlertModal
                    show={showConfirm}
                    onClose={() => setShowConfirm(false)}
                    onConfirm={handleConfirmToggle}
                    title="Confirm Status Change"
                    message={`Are you sure you want to change the status of this report to ${
                      currentReport.paymentStatus === "paid" ? "unpaid" : "paid"
                    }?`}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
