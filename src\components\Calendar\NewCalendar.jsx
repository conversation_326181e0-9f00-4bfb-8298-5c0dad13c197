"use client";

import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CalendarDaysIcon,
  MapPinIcon,
  CalendarIcon,
  UsersIcon,
  ArrowPathIcon,
} from "@heroicons/react/20/solid";
import { Dialog, Transition } from "@headlessui/react";
import React, { useState, useEffect, Fragment } from "react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import "./CalendarStyle.css";
import { QRCode } from "react-qrcode-logo";
import {
  Accordion,
  AccordionHeader,
  AccordionBody,
} from "@material-tailwind/react";
import {
  convertDateIntoIndianFormat,
  convertTime,
} from "@/helpers/dateHelpers";
import Link from "next/link";
import CancelBooking from "../Booking/CancelBooking";
import SuccessNotification from "../Notification/SuccessNotification";
import ErrorNotification from "../Notification/ErrorNotification";
import Logo from "../../../public/KhelSportLogo.png";
import moment from "moment";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const NewCalendar = () => {
  // const currentDate = new Date();

  const [mode, setMode] = useState("single");
  const [selectedDates, setSelectedDates] = useState([new Date()]);

  const [allBookings, setAllBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedBooking, setSelectedBooking] = useState({});
  const [slide, setSlide] = useState(false);

  const [cancelModal, setCancelModal] = useState(false);
  const [message, setMessage] = useState("Booking cancelled successfully");
  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [attendanceModal, setAttendanceModal] = useState(false);

  const [location, setLocation] = useState({});
  const [constBooking, setConstBooking] = useState(null);
  const [attendanceLoading, setAttendanceLoading] = useState(false);
  const [open, setOpen] = React.useState(1);

  // Add state for OTP
  const [verificationOTP, setVerificationOTP] = useState(null);
  const [otpLoading, setOtpLoading] = useState(false);
  const [otpError, setOtpError] = useState(null);
  const [timeWindowTimer, setTimeWindowTimer] = useState(null);

  const handleOpen = (value) => setOpen(open === value ? 0 : value);

  // Cleanup timer on component unmount
  useEffect(() => {
    return () => {
      if (timeWindowTimer) {
        clearInterval(timeWindowTimer);
      }
    };
  }, [timeWindowTimer]);

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  const handleDay = (action) => {
    if (action === "prev") {
      setSlide(true);
      setSelectedDates([
        new Date(selectedDates[0].setDate(selectedDates[0].getDate() - 1)),
      ]);
    }
    if (action === "next") {
      setSlide(true);
      setSelectedDates([
        new Date(selectedDates[0].setDate(selectedDates[0].getDate() + 1)),
      ]);
    }

    if (action === "today") {
      setSelectedDates([new Date()]);
      setSlide(true);
    }
  };

  const getCurrentLocation = () => {
    try {
      if (navigator.geolocation) {
        // get the current users location
        navigator.geolocation.getCurrentPosition(
          (position) => {
            // save the geolocation coordinates in two variables
            const { latitude, longitude } = position.coords;
            // update the value of userlocation variable
            setLocation({ latitude, longitude });
          },
          // if there was an error getting the users location
          (error) => {
            console.error("Error getting user location:", error);
          }
        );
      }
      // if geolocation is not supported by the users browser
      else {
        setErrorNotification(true);
        setMessage(
          "Geolocation is not supported by this browser. Or you denied the permission"
        );
        setSuccessNotification(false);
        setTimeout(() => {
          setErrorNotification(false);
        }, 3000);
        console.error("Geolocation is not supported by this browser.");
      }
    } catch (error) {
      console.log(error);
    }
  };

  const markCoachAttendance = async (data) => {
    try {
      setAttendanceLoading(true);
      let startDate = moment
        .tz(moment(selectedDates[0]), "Asia/Kolkata")
        .format()
        .split("T")[0];
      startDate = moment
        .tz(moment(`${startDate}T${data.startTime}`), "Asia/Kolkata")
        .format();

      let endDate = moment
        .tz(moment(selectedDates[0]), "Asia/Kolkata")
        .format()
        .split("T")[0];
      endDate = moment
        .tz(moment(`${endDate}T${data.endTime}`), "Asia/Kolkata")
        .format();

      const currentDate = moment.tz(moment(), "Asia/Kolkata").format();

      const showButton = moment(currentDate).isBetween(
        moment(startDate).subtract(15, "minutes"),
        moment(endDate).add(15, "minutes")
      );

      if (showButton) {
        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        let requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: JSON.stringify({
            ...data,
            lat: location?.latitude,
            long: location?.longitude,
          }),
        };

        if (!location || !location.latitude || !location.longitude) {
          console.log("enter if");
          setAttendanceLoading(false);
          setErrorNotification(true);
          setMessage("Unable to get the location");
          setSuccessNotification(false);
          setTimeout(() => {
            setErrorNotification(false);
          }, 3000);
          return;
        }

        const response = await fetch(`/api/attendance`, requestOptions);

        const result = await response.json();
        if (!result.error) {
          setAttendanceLoading(false);
          setSuccessNotification(true);
          setMessage("Attendance marked successfully");
          setErrorNotification(false);
          setTimeout(() => {
            setSuccessNotification(false);
          }, 3000);
        } else {
          if (result?.error?.status == 400) {
            setAttendanceLoading(false);
            setErrorNotification(true);
            setMessage("Location not matched");
            setSuccessNotification(false);
            setTimeout(() => {
              setErrorNotification(false);
            }, 3000);
          } else {
            setAttendanceLoading(false);
            setErrorNotification(true);
            setMessage("Please provide the location");
            setSuccessNotification(false);
            setTimeout(() => {
              setErrorNotification(false);
            }, 3000);
          }
        }
      } else {
        setAttendanceLoading(false);
        setErrorNotification(true);
        setMessage(
          "You can mark your attendance from 15 minutes prior to the course start time until 15 minutes after the course end time."
        );
        setSuccessNotification(false);
        setTimeout(() => {
          setErrorNotification(false);
        }, 5000);
      }
    } catch (error) {
      console.log(error);
      setAttendanceLoading(false);
      setErrorNotification(true);
      setMessage("Something went wrong.");
      setSuccessNotification(false);
      setTimeout(() => {
        setErrorNotification(false);
      }, 5000);
    }
  };
  // Helper function to find the next upcoming session within OTP time window
  const findValidSessionForOtp = () => {
    if (!selectedDates[0] || !allBookings || allBookings.length === 0) {
      return null;
    }

    const currentDate = moment.tz(moment(), "Asia/Kolkata");
    const selectedDateStr = moment
      .tz(moment(selectedDates[0]), "Asia/Kolkata")
      .format()
      .split("T")[0];

    // Find all sessions for the selected date
    const todaySessions = [];

    allBookings.forEach(booking => {
      if (booking.bookings && booking.bookings.length > 0) {
        booking.bookings.forEach(session => {
          // Check if session is on selected date and is upcoming
          const sessionDate = moment.tz(moment(session.date || selectedDates[0]), "Asia/Kolkata").format().split("T")[0];

          if (sessionDate === selectedDateStr && session.status === "upcoming") {
            const sessionStartTime = moment.tz(
              moment(`${selectedDateStr}T${session.startTime || booking.startTime}`),
              "Asia/Kolkata"
            );
            const sessionEndTime = moment.tz(
              moment(`${selectedDateStr}T${session.endTime || booking.endTime}`),
              "Asia/Kolkata"
            );

            // Check if current time is within 15 minutes before start and 15 minutes after end
            const isWithinWindow = currentDate.isBetween(
              sessionStartTime.clone().subtract(15, "minutes"),
              sessionEndTime.clone().add(15, "minutes")
            );

            // Check if player attendance is not already marked
            const isAttendanceNotMarked = session.playerAttendance !== "present";

            if (isWithinWindow && isAttendanceNotMarked) {
              todaySessions.push({
                ...session,
                startTime: session.startTime || booking.startTime,
                endTime: session.endTime || booking.endTime,
                courseId: booking.id,
                maxGroupSize: booking.maxGroupSize,
                sessionStartMoment: sessionStartTime
              });
            }
          }
        });
      }
    });

    // Sort by start time and return the earliest valid session
    if (todaySessions.length > 0) {
      todaySessions.sort((a, b) => a.sessionStartMoment.diff(b.sessionStartMoment));
      return todaySessions[0];
    }

    return null;
  };



  // Add function to generate OTP
  const generateVerificationOTP = async (attendanceData) => {
    setOtpLoading(true);
    setOtpError(null);

    // Validate required data
    if (!attendanceData.bookingId) {
      setOtpError("Booking ID is missing");
      setOtpLoading(false);
      return;
    }

    if (!attendanceData.playerId) {
      setOtpError("Player ID is missing");
      setOtpLoading(false);
      return;
    }

    if (!attendanceData.courseId) {
      setOtpError("Course ID is missing");
      setOtpLoading(false);
      return;
    }

    if (!attendanceData.classId) {
      setOtpError("Class ID is missing");
      setOtpLoading(false);
      return;
    }

    try {
      const response = await fetch(`/api/attendance/generateOtpPlayer`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          courseId: attendanceData.courseId,
          playerId: attendanceData.playerId,
          classId: attendanceData.classId,
          bookingId: attendanceData.bookingId,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setVerificationOTP({ otp: result.otp });
      } else {
        setOtpError(result.error || "Failed to generate OTP");
      }
    } catch (error) {
      setOtpError("Network error occurred");
      console.error("OTP generation error:", error);
    } finally {
      setOtpLoading(false);
    }
  };

  // Function to monitor time window and update OTP accordingly
  const startTimeWindowMonitoring = () => {
    // Clear any existing timer
    if (timeWindowTimer) {
      clearInterval(timeWindowTimer);
    }

    const timer = setInterval(() => {
      // Check for valid session each time
      const validSession = findValidSessionForOtp();

      if (validSession && !verificationOTP && !otpLoading) {
        // We have a valid session but no OTP - generate it
        const sessionData = {
          startTime: validSession.startTime,
          endTime: validSession.endTime,
          bookingId: validSession.id,
          classId: validSession.classId,
          playerId: validSession.playerId,
          courseId: validSession.courseId,
          maxGroupSize: validSession.maxGroupSize
        };
        generateVerificationOTP(sessionData);
      } else if (!validSession && verificationOTP) {
        // No valid session but we have OTP - clear it
        setVerificationOTP(null);
        setOtpError("OTP is only available 15 minutes before class starts until 15 minutes after class ends");
      }
    }, 30000); // Check every 30 seconds

    setTimeWindowTimer(timer);
  };

  // Function to stop time window monitoring
  const stopTimeWindowMonitoring = () => {
    if (timeWindowTimer) {
      clearInterval(timeWindowTimer);
      setTimeWindowTimer(null);
    }
  };

  const checkAttendanceShowTime = (attendanceData) => {
    // First, find the valid session for OTP generation
    const validSession = findValidSessionForOtp();

    if (!validSession) {
      // Check if attendance is already marked for this session
      const isAttendanceMarked = attendanceData.playerAttendance === "present";

      if (isAttendanceMarked) {
        setOtpError("Attendance has already been marked for this session");
      } else {
        setOtpError("OTP is only available 15 minutes before class starts until 15 minutes after class ends");
      }

      setAttendanceModal(true);
      setVerificationOTP(null);
      return;
    }

    // Use the valid session data for OTP generation
    const sessionData = {
      ...attendanceData,
      startTime: validSession.startTime,
      endTime: validSession.endTime,
      bookingId: validSession.id,
      classId: validSession.classId,
      playerId: validSession.playerId,
      courseId: validSession.courseId,
      maxGroupSize: validSession.maxGroupSize
    };

    setAttendanceDetails(sessionData);
    setAttendanceModal(true);
    setVerificationOTP(null);
    setOtpError(null);

    // Generate OTP for the valid session
    generateVerificationOTP(sessionData);

    // Start monitoring time window
    startTimeWindowMonitoring();
  };

  const handleDateChange = (date) => {

    if (mode === "single") {
      setSelectedDates([date]);
    } else {
      setSelectedDates(date);
    }
  };

  const getLatestBookings = async () => {
    try {
      setLoading(true);
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let data = { date: formatDateToYYYYMMDD(selectedDates[0]) };

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(data),
      };
      const response = await fetch(
        `/api/bookings/latest-bookings`,
        requestOptions
      );

      const result = await response.json();
      if (!result.error) {
        setAllBookings(result);
        const firstNonEvent = result.find((item) => item?.type !== "event");
        if (constBooking) {
          const data = result.find((item) => item.id === constBooking.id);
          if (!data) {
            setSelectedBooking({ ...firstNonEvent, idx: 0 });
          } else {
            setSelectedBooking({ ...data, idx: constBooking.idx });
          }
        } else {
          if (firstNonEvent) {
            setSelectedBooking({ ...firstNonEvent, idx: 0 });
          } else {
            setSelectedBooking({});
          }
        }

        setLoading(false);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  useEffect(() => {
    getLatestBookings();
  }, [
    selectedDates[0],
    successNotification,
    setSuccessNotification,
    errorNotification,
    setErrorNotification,
  ]);

  return (
    <div>
      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}

      {attendanceModal && (
        <Attendance
          attendanceModal={attendanceModal}
          setAttendanceModal={setAttendanceModal}
          verificationOTP={verificationOTP}
          otpLoading={otpLoading}
          otpError={otpError}
          generateVerificationOTP={generateVerificationOTP}
          stopTimeWindowMonitoring={stopTimeWindowMonitoring}
          findValidSessionForOtp={findValidSessionForOtp}
        />
      )}

      {cancelModal && (
        <CancelBooking
          cancelModal={cancelModal}
          setCancelModal={setCancelModal}
          setSuccessNotification={setSuccessNotification}
          setErrorNotification={setErrorNotification}
          setMessage={setMessage}
          selectedBooking={selectedBooking}
          selectedDates={selectedDates}
        />
      )}
      <header className="flex items-center border-gray-200 px-12 py-[1.2rem] justify-between ">
        <div className="flex items-center w-[462px] gap-[1px]">
          <button
            type="button"
            onClick={() => handleDay("prev")}
            className="rounded-full bg-white border border-black"
          >
            <span className="sr-only">Previous day</span>
            <ChevronLeftIcon className="h-6 w-6" aria-hidden="true" />
          </button>{" "}
          &nbsp; &nbsp;
          <button
            type="button"
            onClick={() => handleDay("next")}
            className="rounded-full bg-white border border-black"
          >
            <span className="sr-only">Next day</span>
            <ChevronRightIcon className="h-6 w-6" aria-hidden="true" />
          </button>{" "}
          &nbsp; &nbsp;
          {/* <div className="relative flex items-center rounded-md bg-white shadow-sm md:items-stretch"> */}
          <button
            type="button"
            onClick={() => handleDay("today")}
            className="bg-white border border-[#979797] pl-2 pr-2 py-[3px] rounded-md"
          >
            Today
          </button>{" "}
          &nbsp; &nbsp;
          <h1 className="text-base font-semibold leading-6 text-gray-900">
            <time dateTime="2022-01-22" className="sm:hidden">
              {`${selectedDates[0].getDate()} ${
                months[selectedDates[0].getMonth()]
              } ${selectedDates[0].getFullYear()}`}
            </time>
            <time
              dateTime={`${formatDateToYYYYMMDD(selectedDates[0])}`}
              className="hidden sm:inline"
            >
              {`${selectedDates[0].getDate()} ${
                months[selectedDates[0].getMonth()]
              } ${selectedDates[0].getFullYear()}`}
            </time>
          </h1>{" "}
          &nbsp; &nbsp;
          <div>
            <span className="isolate inline-flex rounded-md shadow-sm"></span>
          </div>
        </div>
      </header>

      <div className="m-0 flex flex-col md:flex-row md:ml-12 rounded gap-6">
        <div className="border-b border-gray-200 bg-white py-5 sm:px-6 flex w-full justify-center md:w-1/2">
          <div className="flex flex-col w-full ml-[24px]">
            <Calendar
              className="custom-calendar"
              onChange={handleDateChange}
              tileClassName={"border-solid"}
              prev2Label={null}
              next2Label={null}
              value={selectedDates}
              selectRange={mode === "multiple"}
              onActiveStartDateChange={() => setSlide(false)}
              activeStartDate={slide ? selectedDates[0] : false}
            />
            <span className="flex items-center mt-10 text-[18px]">
              <CalendarDaysIcon className="h-10 w-10 mr-2" aria-hidden="true" />{" "}
              Upcoming
            </span>
            <br />
            <div className="max-h-[52vh] overflow-auto ml-[-30px]">
              {allBookings && allBookings.length > 0 ? (
                allBookings?.map((booking, bookingIdx) => {
                  return (
                    booking?.type && (
                      <div className="w-full" key={bookingIdx}>
                        <Accordion
                          className="w-full"
                          //  open={open === 1}
                        >
                          <AccordionHeader
                            className={`py-6 shadow-sm pl-10 pr-2 ${
                              bookingIdx === selectedBooking.idx
                                ? "bg-blue-50"
                                : null
                            }`}
                            onClick={() => {
                              if (
                                booking?.type === "class" ||
                                booking?.type === "course"
                              ) {
                                setSelectedBooking({
                                  ...booking,
                                  idx: bookingIdx,
                                });
                                setConstBooking({
                                  ...booking,
                                  idx: bookingIdx,
                                });
                              }
                            }}
                          >
                            <div className="w-full">
                              <header className="flex justify-between">
                                {booking.name.length > 50
                                  ? `${booking.name.slice(0, 50)}...`
                                  : booking.name}
                                {booking.type == "class" && (
                                  <span className="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                                    Class
                                  </span>
                                )}
                                {booking.type == "course" && (
                                  <span className="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-800 ring-1 ring-inset ring-yellow-600/20">
                                    Course
                                  </span>
                                )}
                                {booking.type == "event" && (
                                  <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                                    Break
                                  </span>
                                )}
                              </header>
                              <span className="flex flex-row text-xs text-[#979797] mt-4 items-center gap-[.8rem]">
                                <CalendarIcon
                                  className="h-5 w-5"
                                  aria-hidden="true"
                                />
                                <time className="text-[14.7px] font-light	">
                                  {convertDateIntoIndianFormat(
                                    new Date(selectedDates[0])
                                  )}
                                  &nbsp; at &nbsp;
                                  {`${convertTime(
                                    booking?.startTime
                                  )} : ${convertTime(booking?.endTime)}`}
                                </time>
                              </span>
                            </div>
                          </AccordionHeader>
                        </Accordion>
                      </div>
                    )
                  );
                })
              ) : (
                <span className="ml-[32px]">No Bookings Found</span>
              )}
            </div>
          </div>
        </div>

        <div className="md:w-1/2 flex flex-col sm:px-6">
          <div className="border-b border-gray-200 bg-white w-full py-5 h-fit ml-1 rounded">
            {/* Image and course   */}
            {selectedBooking && selectedBooking.type ? (
              <>
                {/* {console.log(selectedBooking)} */}
                <Accordion className="w-full" open={true}>
                  <AccordionHeader
                    className="w-full justify-between px-4"
                    onClick={() => handleOpen(1)}
                  >
                    <div className="w-full">
                      <div className="mt-[-20px] mb-3 flex justify-end">
                        {loading ? (
                          <div role="status">
                            <svg
                              aria-hidden="true"
                              className="inline w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-green-500"
                              viewBox="0 0 100 101"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="currentColor"
                              />
                              <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentFill"
                              />
                            </svg>
                            <span className="sr-only">Loading...</span>
                          </div>
                        ) : (
                          <button
                            className="text-[16px] text-green-500 flex flex-row"
                            onClick={() => {
                              getLatestBookings();
                            }}
                          >
                            <ArrowPathIcon
                              className="h-6 w-6"
                              aria-hidden="true"
                            />
                            Refresh
                          </button>
                        )}
                      </div>
                      <header className="flex justify-between w-full">
                        <div className="flex gap-4">
                          <div>
                            {/* {console.log(selectedBooking.image)} */}
                            <img
                              className=" md:w-20 md:h-20 md:rounded max-sm:hidden object-cover"
                              src={selectedBooking.image}
                              // src="https://t4.ftcdn.net/jpg/00/77/44/67/360_F_77446709_6WV0FO6YcjAOZcbUO5WMi5PpAtChKaGG.jpg"
                              alt="Course Image"
                            />
                          </div>
                          <div className="flex flex-col gap-[.5rem]">
                            <span className="text-[19px] capitalize">
                              {selectedBooking?.name}
                            </span>
                            <time className="text-[15px] text-[#0068FF]">
                              {convertTime(selectedBooking?.startTime)} -{" "}
                              {convertTime(selectedBooking?.endTime)}
                            </time>
                            <span className="text-gray-500 flex flex-row text-xs items-center gap-[.5rem]">
                              <MapPinIcon
                                className="h-4 w-4"
                                aria-hidden="true"
                              />
                              <p className="text-[15px] capitalize">
                                {selectedBooking?.facility}
                              </p>
                            </span>
                          </div>
                        </div>
                        {selectedBooking?.type == "class" && (
                          <>
                            <div className="flex flex-col text-xs text-gray-400 gap-4">
                              <span className="text-[14px] whitespace-nowrap">
                                {convertDateIntoIndianFormat(
                                  new Date(selectedDates[0])
                                )}
                              </span>
                              <span className="text-[14px] whitespace-nowrap">
                                No. of Bookings:{" "}
                                {/* {selectedBooking?.playerEnrolled} */}
                                {selectedBooking?.bookings.length}
                              </span>
                            </div>
                          </>
                        )}
                        {selectedBooking?.type == "course" && (
                          <>
                            <div className="flex flex-col text-base text-gray-400 items-end">
                              <span className="from-neutral-50 whitespace-nowrap">
                                {convertDateIntoIndianFormat(selectedDates[0])}
                              </span>
                              <span className="mt-2 text-green-500">
                                ₹{selectedBooking.coursePrice}
                              </span>
                              <span className="mt-2  flex flex-row">
                                <UsersIcon
                                  className="h-4 w-4 mt-1 mr-2"
                                  aria-hidden="true"
                                />
                                {/* {console.log(selectedBooking, "selected")} */}

                                <span className="text-green-500">
                                  {`${selectedBooking.playerEnrolled}/${selectedBooking.maxGroupSize}`}
                                </span>
                              </span>
                            </div>
                          </>
                        )}
                      </header>
                      {selectedBooking?.type == "course" && (
                        <div className="flex justify-between text-sm mt-5">
                          {attendanceLoading ? (
                            <div role="status">
                              <svg
                                aria-hidden="true"
                                className="inline w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-green-500"
                                viewBox="0 0 100 101"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                  fill="currentColor"
                                />
                                <path
                                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                  fill="currentFill"
                                />
                              </svg>
                              <span className="sr-only">Loading...</span>
                            </div>
                          ) : (
                            <button
                              className={`py-[5px] px-[8px] border border-green-500 bg-green-50 text-green-500 rounded ${
                                (selectedBooking?.bookings[0]?.status !==
                                  "upcoming" ||
                                  selectedBooking?.bookings[0]
                                    ?.coachAttendance == "present") &&
                                "cursor-not-allowed"
                              }`}
                              disabled={
                                selectedBooking?.bookings[0]?.status !==
                                  "upcoming" ||
                                selectedBooking?.bookings[0]?.coachAttendance ==
                                  "present"
                              }
                              onClick={() => {
                                markCoachAttendance({
                                  startTime: selectedBooking.startTime,
                                  endTime: selectedBooking.endTime,
                                  courseId: selectedBooking.id,
                                  maxGroupSize: selectedBooking.maxGroupSize,
                                  bookingId:
                                    selectedBooking.maxGroupSize === 1
                                      ? selectedBooking?.bookings[0]?.id
                                      : "",
                                  Class:
                                    selectedBooking.maxGroupSize === 1
                                      ? selectedBooking?.bookings[0]?.classId
                                      : "",
                                  playerId:
                                    selectedBooking.maxGroupSize === 1
                                      ? selectedBooking?.bookings[0]?.playerId
                                      : "",
                                });
                              }}
                            >
                              Mark your attendance
                            </button>
                          )}
                          <button
                            className="py-[5px] px-[8px] border border-blue-500 bg-blue-50 text-blue-500 rounded"
                            disabled={
                              selectedBooking?.bookings[0]?.status !==
                              "upcoming"
                            }
                            onClick={() => {
                              checkAttendanceShowTime({
                                startTime: selectedBooking.startTime,
                                endTime: selectedBooking.endTime,
                                bookingId: selectedBooking?.bookings[0]?.id,
                                classId: selectedBooking?.bookings[0]?.classId,
                                playerId:
                                  selectedBooking?.bookings[0]?.playerId,
                                maxGroupSize: selectedBooking.maxGroupSize,
                                courseId: selectedBooking.id,
                              });
                            }}
                          >
                            Mark player attendance
                          </button>
                        </div>
                      )}
                    </div>
                  </AccordionHeader>
                  <AccordionBody>
                    {selectedBooking &&
                    selectedBooking?.bookings &&
                    selectedBooking?.bookings.length > 0 ? (
                      <>
                        {/* {console.log(selectedBooking)} */}
                        <div className="overflow-hidden rounded-md border-bottom border-gray-300 bg-slate-50">
                          <ul
                            role="list"
                            className="divide-y divide-gray-300 max-h-[150px] overflow-auto"
                          >
                            {selectedBooking?.bookings?.map((item, idx) => (
                              <div key={idx}>
                                <li className="px-6 py-4 bg-white">
                                  <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-4">
                                      <strong className="text-black text-[16px]">
                                        {item.name}
                                      </strong>
                                      {item.status === "upcoming" && (
                                        <span className="inline-flex items-center rounded-full bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-800 ring-1 ring-inset ring-yellow-600/20 max-sm:mr-[33px]">
                                          Upcoming
                                        </span>
                                      )}
                                      {item.status === "completed" && (
                                        <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20 max-sm:mr-[33px]">
                                          Completed
                                        </span>
                                      )}
                                      {item.status === "cancelled" && (
                                        <span className="inline-flex items-center rounded-full bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10 max-sm:mr-[33px]">
                                          Cancelled
                                        </span>
                                      )}
                                      {item.status === "rescheduled" && (
                                        <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10 max-sm:mr-[33px]">
                                          Rescheduled
                                        </span>
                                      )}
                                    </div>
                                    {selectedBooking?.type === "class" && (
                                      <div className="flex gap-2">
                                        <time className="text-[#0068FF] text-[16px]">
                                          <Link href={`/bookings/${item?.id}`}>
                                            {convertTime(item.startTime)} -{" "}
                                            {convertTime(item.endTime)}
                                          </Link>
                                        </time>
                                        <span className="text-[#0068FF] text-[16px]">
                                          |
                                        </span>
                                        <span className="text-[#60D669] text-[16px]">
                                          ₹{item?.pricePaid}
                                        </span>
                                      </div>
                                    )}

                                    {selectedBooking?.type === "course" && (
                                      <>
                                        <Link
                                          className="text-blue-500"
                                          href={`/bookings/${item?.id}`}
                                        >
                                          Details
                                        </Link>
                                      </>
                                    )}
                                  </div>

                                  {selectedBooking?.type === "course" && (
                                    <div className="flex justify-between text-sm mt-5">
                                      {item.coachAttendance === "present" ? (
                                        <div className="flex items-center gap-4">
                                          <p className="text-black text-[14px]">
                                            Coach:
                                          </p>
                                          <span className="text-[14px] font-semibold text-green-500">
                                            Present
                                          </span>
                                        </div>
                                      ) : item.coachAttendance === "absent" ? (
                                        <div className="flex items-center gap-4">
                                          <strong className="text-black text-[14px]">
                                            Coach:
                                          </strong>
                                          <span className="text-[14px] font-semibold text-red-500">
                                            Absent
                                          </span>
                                        </div>
                                      ) : null}

                                      {item.attendance === "present" ? (
                                        <div className="flex items-center gap-4">
                                          <span className="text-black text-[14px]">
                                            Player:
                                          </span>
                                          <span className="text-[14px] font-semibold text-blue-500">
                                            Present
                                          </span>
                                        </div>
                                      ) : item.attendance === "absent" ? (
                                        <div className="flex items-center gap-4">
                                          <span className="text-black text-[14px]">
                                            Player:
                                          </span>
                                          <span className="text-[14px] font-semibold text-red-500">
                                            Absent
                                          </span>
                                        </div>
                                      ) : null}
                                    </div>
                                  )}

                                  {selectedBooking?.type === "class" && (
                                    <div className="flex justify-between text-sm mt-5">
                                      {item.coachAttendance === "present" ? (
                                        <div className="flex items-center gap-4">
                                          <p className="text-black text-[14px]">
                                            Coach:
                                          </p>
                                          <span className="text-[14px] font-semibold text-green-500">
                                            Present
                                          </span>
                                        </div>
                                      ) : item.coachAttendance === "absent" ? (
                                        <div className="flex items-center gap-4">
                                          <strong className="text-black text-[14px]">
                                            Coach:
                                          </strong>
                                          <span className="text-[14px] font-semibold text-red-500">
                                            Absent
                                          </span>
                                        </div>
                                      ) : attendanceLoading ? (
                                        <div role="status">
                                          <svg
                                            aria-hidden="true"
                                            className="inline w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-green-500"
                                            viewBox="0 0 100 101"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                          >
                                            <path
                                              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                              fill="currentColor"
                                            />
                                            <path
                                              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                              fill="currentFill"
                                            />
                                          </svg>
                                          <span className="sr-only">
                                            Loading...
                                          </span>
                                        </div>
                                      ) : (
                                        <button
                                          className={`py-[5px] px-[8px] border border-green-500 bg-green-50 text-green-500 rounded ${
                                            item?.status !== "upcoming" &&
                                            "cursor-not-allowed"
                                          }`}
                                          disabled={item?.status !== "upcoming"}
                                          onClick={() => {
                                            markCoachAttendance({
                                              startTime: item.startTime,
                                              endTime: item.endTime,
                                              bookingId: item.id,
                                              Class: item.classId,
                                              playerId: item.playerId,
                                              maxGroupSize:
                                                selectedBooking.maxGroupSize,
                                              courseId:
                                                selectedBooking.courseId,
                                            });
                                          }}
                                        >
                                          Mark your attendance
                                        </button>
                                      )}

                                      {item.attendance === "present" ? (
                                        <div className="flex items-center gap-4">
                                          <span className="text-black text-[14px]">
                                            Player:
                                          </span>
                                          <span className="text-[14px] font-semibold text-blue-500">
                                            Present
                                          </span>
                                        </div>
                                      ) : item.attendance === "absent" ? (
                                        <div className="flex items-center gap-4">
                                          <span className="text-black text-[14px]">
                                            Player:
                                          </span>
                                          <span className="text-[14px] font-semibold text-red-500">
                                            Absent
                                          </span>
                                        </div>
                                      ) : (
                                        <button
                                          className="py-[5px] px-[8px] border border-blue-500 bg-blue-50 text-blue-500 rounded"
                                          disabled={item?.status !== "upcoming"}
                                          onClick={() => {
                                            checkAttendanceShowTime({
                                              startTime: item.startTime,
                                              endTime: item.endTime,
                                              bookingId: item.id,
                                              classId: item.classId,
                                              playerId: item.playerId,
                                              maxGroupSize:
                                                selectedBooking.maxGroupSize,
                                              courseId: selectedBooking.id,
                                            });
                                          }}
                                        >
                                          Mark player attendance
                                        </button>
                                      )}
                                    </div>
                                  )}
                                </li>
                              </div>
                            ))}
                          </ul>
                        </div>
                      </>
                    ) : (
                      <>
                        <span className="text-center md:ml-[10px] ml-[15px]">
                          No Bookings Found
                        </span>
                      </>
                    )}
                  </AccordionBody>
                </Accordion>
              </>
            ) : (
              <span className="ml-[10px]">No Bookings Found</span>
            )}
          </div>
          <div className="flex justify-end mt-4">
            {selectedBooking && selectedBooking.maxGroupSize == 1 && (
              <button
                className="text-white bg-gray-700 hover:bg-black px-3 py-2 rounded-md"
                onClick={() => {
                  setCancelModal(!cancelModal);
                }}
              >
                Cancel
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewCalendar;

function Attendance({
  attendanceModal,
  setAttendanceModal,
  verificationOTP,
  otpLoading,
  otpError,
  generateVerificationOTP,
  stopTimeWindowMonitoring,
  findValidSessionForOtp,
}) {
  const handleCloseModal = () => {
    stopTimeWindowMonitoring();
    setAttendanceModal(false);
  };

  const handleRegenerateOTP = () => {
    // Find the current valid session
    const validSession = findValidSessionForOtp();

    if (validSession) {
      const sessionData = {
        startTime: validSession.startTime,
        endTime: validSession.endTime,
        bookingId: validSession.id,
        classId: validSession.classId,
        playerId: validSession.playerId,
        courseId: validSession.courseId,
        maxGroupSize: validSession.maxGroupSize
      };
      generateVerificationOTP(sessionData);
    }
  };
  return (
    <Transition.Root show={attendanceModal} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10"
        onClose={handleCloseModal}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                <div>
                  <div className="mt-3 text-center sm:mt-5">
                    <Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Player Attendance Verification
                    </Dialog.Title>
                    <div className="mt-4 flex flex-col justify-center items-center">
                      {otpLoading ? (
                        <div className="flex flex-col items-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                          <p className="mt-2 text-sm text-gray-600">
                            Generating verification code...
                          </p>
                        </div>
                      ) : otpError ? (
                        <div className="text-center">
                          <p className="text-red-600 text-sm mb-3">
                            {otpError}
                          </p>
                          {findValidSessionForOtp() && (
                            <button
                              type="button"
                              className="inline-flex justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                              onClick={handleRegenerateOTP}
                            >
                              Try Again
                            </button>
                          )}
                        </div>
                      ) : verificationOTP ? (
                        <div className="text-center">
                          <p className="text-sm text-gray-600 mb-4">
                            Share this verification code with the player:
                          </p>
                          <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-6 mb-4">
                            <div className="text-3xl font-bold text-indigo-600 tracking-wider">
                              {verificationOTP.otp}
                            </div>
                          </div>
                          {findValidSessionForOtp() && (
                            <button
                              type="button"
                              className="inline-flex justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                              onClick={handleRegenerateOTP}
                            >
                              Generate New OTP
                            </button>
                          )}
                        </div>
                      ) : (
                        <div className="text-center">
                          <p className="text-sm text-gray-600 mb-3">
                            Verification code will be available 15 minutes before
                            class starts until 15 minutes after class ends.
                          </p>
                          {findValidSessionForOtp() && (
                            <button
                              type="button"
                              className="inline-flex justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                              onClick={handleRegenerateOTP}
                            >
                              Generate OTP
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-6 flex justify-center">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                    onClick={handleCloseModal}
                  >
                    Close
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
