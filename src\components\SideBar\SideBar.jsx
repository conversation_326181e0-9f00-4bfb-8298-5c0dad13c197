"use client";
import { Fragment, useEffect, useState } from "react";
import { Dialog, Transition, Disclosure } from "@headlessui/react";
import {
  CalendarIcon,
  DocumentDuplicateIcon,
  HomeIcon,
  UsersIcon,
  XMarkIcon,
  EnvelopeIcon,
  ChartBarIcon
} from "@heroicons/react/24/outline";
import { ChevronRightIcon } from "@heroicons/react/20/solid";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";

import '../../../public/Khel-1.png'
function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function SideBar({ sidebarOpen, setSidebarOpen }) {
  const [coachDetails, setCoachDetails] = useState({});
  const [coachVerified, setCoachVerified] = useState(false);
  const [isCalendarLinked, setIsCalendarLinked] = useState(false);

  const router = useRouter();
  const pathname = usePathname()

  const getCoachDetails = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };

      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
      if (!result.error) {
        setCoachDetails(result);
        if (result.status === "active") {
          setCoachVerified(true);
        } else {
          setCoachVerified(false);
        }
        if (
          result.googleEmail &&
          result.googleEmail !== "" &&
          result.refreshToken &&
          result.refreshToken !== ""
        ) {
          setIsCalendarLinked(true);
        } else {
          setIsCalendarLinked(false);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCoachDetails();
  }, [pathname]);

  const navigation = coachVerified
    ? [
        { name: "DashBoard", href: "/dashboard", icon: HomeIcon },
        { name: "Calendar", href: "/calendar", icon: CalendarIcon },

        {
          name: "Training Schedule",
          // href: "/courses",
          icon: UsersIcon,
          children: [
            { name: "Listing", href: "/course/list" },
            {
              name: "Create",
              href: isCalendarLinked ? "/course/create" : "/dashboard",
            },
          ],
        },

        {
          name: "Booking Details",
          href: "/bookings",
          icon: DocumentDuplicateIcon,
        },
        {
          name: "Profile",
          // href: "/profile",
          icon: UsersIcon,
          children: [
            { name: "Basic Details", href: "/profile/basic_details" },
            {
              name: "Professional Details",
              href: "/profile/professional_details",
            },
            { name: "KYC Details", href: "/profile/kyc_details" },
          ],
        },
        { name: "Contact Us", href: "/contact", icon: EnvelopeIcon },
        { name: "Reports", href: "/reports", icon: ChartBarIcon },

        //Reports section in tab
        // {
        //   name: "Reports",
        //   href: "/dashboard/bookings",
        //   icon: DocumentDuplicateIcon,
        // },

        // add more side nav ...
      ]
    : [];

  return (
    <>
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50 lg:hidden"
          onClose={setSidebarOpen}
        >
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon
                        className="h-6 w-6 text-white"
                        aria-hidden="true"
                      />
                    </button>
                  </div>
                </Transition.Child>
                {/* Sidebar Mobile */}
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-black px-6 pb-2">
                  <Link
                    onClick={() => setSidebarOpen(false)}
                    className="flex h-16 shrink-0 items-center cursor-pointer"
                    href="/dashboard"
                  >
                    <Image
                      priority
                      // onClick={() => {
                      //   console.log("tuoittioi");
                      //   router.push("/dashboard");
                      // }}
                      src="/Khel-1.png"
                      alt="KhelCoach"
                      width={120}
                      height={120}
                      style={{ width: "auto", height: "auto" }}
                    />
                  </Link>
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-7">
                      <li>
                        <ul role="list" className="-mx-2 space-y-1">
                          {navigation.map((item, index) => (
                            <li key={index}>
                              {!item.children ? (
                                <Link
                                  href={item.href}
                                  onClick={() => setSidebarOpen(false)}
                                  className={`link group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold ${
                                    pathname === item.href
                                      ? "bg-white text-indigo-600"
                                      : "text-white hover:text-indigo-600 hover:bg-gray-50"
                                  }`}
                                >
                                  <item.icon
                                    className="h-6 w-6 shrink-0 text-white"
                                    aria-hidden="true"
                                  />
                                  {item.name}
                                </Link>
                              ) : (
                                <Disclosure as="div">
                                  {({ open }) => (
                                    <>
                                      <Disclosure.Button
                                        className={classNames(
                                          item.current
                                            ? "bg-gray-50 text-indigo-600"
                                            : " text-white 0",
                                          "flex items-center w-full text-left rounded-md p-2 gap-x-3 text-sm leading-6 font-semibold text-white"
                                        )}
                                      >
                                        <item.icon
                                          className="h-6 w-6 shrink-0 text-white"
                                          aria-hidden="true"
                                        />
                                        {item.name}
                                        <ChevronRightIcon
                                          className={classNames(
                                            open
                                              ? "rotate-90 text-white"
                                              : "text-white",
                                            "ml-auto h-5 w-5 shrink-0"
                                          )}
                                          aria-hidden="true"
                                        />
                                      </Disclosure.Button>
                                      <Disclosure.Panel
                                        as="ul"
                                        className="mt-1 px-2"
                                      >
                                        {item.children.map((subItem) => (
                                          <li key={subItem.name}>
                                            <Disclosure.Button
                                              as="a"
                                              href={subItem.href}
                                              onClick={() =>
                                                setSidebarOpen(false)
                                              }
                                              className={classNames(
                                                subItem.current
                                                  ? "bg-gray-50 text-indigo-600"
                                                  : " text-white ",
                                                "block rounded-md py-2 pr-2 pl-9 text-sm leading-6 text-white"
                                              )}
                                            >
                                              {subItem.name}
                                            </Disclosure.Button>
                                          </li>
                                        ))}
                                      </Disclosure.Panel>
                                    </>
                                  )}
                                </Disclosure>
                              )}
                            </li>
                          ))}
                        </ul>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>
      {/*SideBar Desktop*/}
      <div className="hidden md:block bg-black LeftSidebar lg:inset-y-0 lg:z-50 lg:flex lg:w-1/5 2xl:w-2/12 lg:flex-col items-center">
        {/* Sidebar component, swap this element with another sidebar if you like */}
        <div className="flex grow flex-col gap-y-5 border-gray-200 fixed ">
          <Link
            href="/dashboard"
            className="flex h-16 shrink-0 items-center cursor-pointer"
          >
            <Image
              priority
              src="/Khel-1.png"
              alt="KhelCaoch"
              width={120}
              height={120}
              style={{ width: "auto", height: "auto" }}
            />
          </Link>
          <nav className="flex flex-1 flex-col min-w-[200px]">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item, index) => (
                    <li key={index}>
                      {!item.children ? (
                        <Link
                          href={item.href}
                          className={`link group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold ${
                            pathname === item.href
                              ? "bg-gray-50 text-indigo-600"
                              : "text-white hover:text-indigo-600 hover:bg-gray-50"
                          }`}
                        >
                          <item.icon
                            className="h-6 w-6 shrink-0 text-gray-400"
                            aria-hidden="true"
                          />
                          {item.name}
                        </Link>
                      ) : (
                        <Disclosure as="div">
                          {({ open }) => (
                            <>
                              <Disclosure.Button
                                className={classNames(
                                  item.current
                                    ? "bg-gray-50 text-indigo-600"
                                    : "hover:bg-gray-50 text-gray-700 hover:text-indigo-600",
                                  "flex items-center w-full text-left rounded-md p-2 gap-x-3 text-sm leading-6 font-semibold text-white"
                                )}
                              >
                                <item.icon
                                  className="h-6 w-6 shrink-0 text-gray-400"
                                  aria-hidden="true"
                                />
                                {item.name}
                                <ChevronRightIcon
                                  className={classNames(
                                    open
                                      ? "rotate-90 text-white"
                                      : "text-white",
                                    "ml-auto h-5 w-5 shrink-0"
                                  )}
                                  aria-hidden="true"
                                />
                              </Disclosure.Button>
                              <Disclosure.Panel as="ul" className="mt-1 px-2">
                                {item.children.map((subItem) => (
                                  <li key={subItem.name}>
                                    <Disclosure.Button
                                      as="a"
                                      href={subItem.href}
                                      className={classNames(
                                        subItem.current
                                          ? "bg-gray-50 text-indigo-600"
                                          : "hover:bg-gray-50 text-gray-700 hover:text-indigo-600",
                                        "block rounded-md py-2 pr-2 pl-9 text-sm leading-6 text-white"
                                      )}
                                    >
                                      {subItem.name}
                                    </Disclosure.Button>
                                  </li>
                                ))}
                              </Disclosure.Panel>
                            </>
                          )}
                        </Disclosure>
                      )}
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>
      {/*end */}
    </>
  );
}
