"use client";
import { useState, Fragment, useEffect } from "react";
import { Listbox, Transition } from "@headlessui/react";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/react/20/solid";
import { CheckCircleIcon, PencilSquareIcon } from "@heroicons/react/24/outline";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { XCircleIcon } from "@heroicons/react/20/solid";
import dynamic from "next/dynamic";
import "../../../../components/CourseCreation/datePicker.css";
import axios from "axios";
import DatePicker from "react-datepicker";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";


// import Cookies from "js-cookie";

import IndepentClasses from "@/components/CourseCreation/IndependentClasses";
import SpecificClasses from "@/components/CourseCreation/SpecificClasses";

const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
});
import "react-quill/dist/quill.snow.css";
// import IndepentClasses from "@/components/CourseCreation/IndependentClasses";
import API from "@/components/API";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

const proficiencyLevelOption = [
  { id: 0, name: "Beginner", value: "beginner" },
  { id: 1, name: "Intermediate", value: "intermediate" },
  { id: 2, name: "Advance", value: "advance" },
];

const sessionTypeOption = [
  { id: 0, name: "Group", value: "group" },
  { id: 1, name: "Individual", value: "individual" },
];

export default function CreateCourse() {
  const router = useRouter();

  const params = useSearchParams();

  const isCoach = params.get("coach");
  const [courseName, setCourseName] = useState("");
  const [courseNameError, setCourseNameError] = useState(false);
  const [isItCamp, setIsItCamp] = useState(false);
  const [campName, setCampName] = useState("");
  const [campNameError, setCampNameError] = useState(false);
  const [maximumGroupSize, setMaximumGroupSize] = useState(1);
  const [maximumGroupSizeError, setMaximumGroupSizeError] = useState(false);
  const [sessionType, setSessionType] = useState(sessionTypeOption[0]);
  const [categoryType, setCategoryType] = useState({});
  const [categories, setCategories] = useState([]);
  const [courseDescription, setCourseDescription] = useState("");
  const [courseDescriptionError, setCourseDescriptionError] = useState(false);
  const [courseAmeneties, setCourseAmeneties] = useState("");
  const [show, setShow] = useState(false);
  const [show2, setShow2] = useState(false);
  const [btnIsLoading, setBtnIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [facilities, setFacilities] = useState([]);
  const [selectedDays, setSelectedDays] = useState([]);
  const [isEnd, setIsEnd] = useState(false);
  const [timeFrom, setTimeFrom] = useState("");
  const [timeTo, setTimeTo] = useState("");
  const [classType, setClassType] = useState("class");
  const [startDate, setStartDate] = useState("");
  const [startDateError, setStartDateError] = useState(false);
  const [endDate, setEndDate] = useState("");
  const [price, setPrice] = useState("");
  const [priceError, setPriceError] = useState(false);
  const [fees30, setFees30] = useState("");
  const [fees45, setFees45] = useState("");
  const [fees60, setFees60] = useState("");
  const [fees30Error, setFees30Error] = useState(false);
  const [fees45Error, setFees45Error] = useState(false);
  const [fees60Error, setFees60Error] = useState(false);
  const [cancellationPolicy, setCancellationPolicy] = useState("");
  const [carryThings, setCarryThings] = useState("");
  const [timeFromError, setTimeFromError] = useState(false);
  const [timeToError, setTimeToError] = useState(false);
  const [courseFacility, setCourseFacility] = useState({});
  const [categoryError, setCategoryError] = useState(false);
  const [facilityError, setFacilityError] = useState(false);
  const [selectedDaysError, setSelectedDaysError] = useState(false);
  const [bookedSlots, setBookedSlots] = useState([]);
  const [message, setMessage] = useState(
    "There was an errors with your submission, please try again later"
  );
  const [imageLoading, setImageLoading] = useState(false);
  const [coach, setCoach] = useState({});
  const [course, setCourse] = useState({});
  const [selectedCategory, setSelectedCategory] = useState("");
  const [isEditable, setIsEditable] = useState(true);
  const [isCustomImage, setIsCustomImage] = useState(false);
  const [proficiencyLevel, setProficiencyLevel] = useState("");

  const getFacilities = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };
      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();

      if (!result.error) {
        setFacilities(result.linkedFacilities);
        setCoach(result);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // Helper function to check if a day is available in coach's academy availability
  const isDayAvailable = (day) => {
    if (!coach?.academyAvailability?.days) return true; // If no restrictions, allow all days
    return coach.academyAvailability.days.includes(day);
  };

  // Helper function to get coach's available time range
  const getCoachTimeRange = () => {
    if (!coach?.academyAvailability) return { minTime: null, maxTime: null };

    const { startTime, endTime } = coach.academyAvailability;

    let minTime = null;
    let maxTime = null;

    if (startTime) {
      const [hours, minutes] = startTime.split(':');
      minTime = new Date(0, 0, 0, parseInt(hours), parseInt(minutes));
    }

    if (endTime) {
      const [hours, minutes] = endTime.split(':');
      maxTime = new Date(0, 0, 0, parseInt(hours), parseInt(minutes));
    }

    return { minTime, maxTime };
  };

  // Helper function to get coach's available date range
  const getCoachDateRange = () => {
    if (!coach?.academyAvailability) return { minDate: null, maxDate: null };

    const { startDate, endDate } = coach.academyAvailability;

    let minDate = null;
    let maxDate = null;

    if (startDate) {
      minDate = formatDateToYYYYMMDD(startDate);
    }

    if (endDate) {
      maxDate = formatDateToYYYYMMDD(endDate);
    }

    return { minDate, maxDate };
  };

  // Helper function to validate if a date is within coach's available range
  const isDateWithinCoachRange = (date) => {
    if (!coach?.academyAvailability) return true; // If no restrictions, allow all dates

    const coachDateRange = getCoachDateRange();
    if (!coachDateRange.minDate || !coachDateRange.maxDate) return true;

    return date >= coachDateRange.minDate && date <= coachDateRange.maxDate;
  };

  const checkEditable = () => {
    const courseDate = new Date(course?.dates?.startDate.split("T")[0]);
    courseDate.setHours(0, 0, 0, 0);
    const todayDate = new Date();
    todayDate.setHours(0, 0, 0, 0);
    if (
      todayDate.getTime() >= courseDate.getTime() ||
      Number(course.playerEnrolled) !== 0
    ) {
      return true;
    } else {
      return false;
    }
  };

  const getCurrentTime = () => {
    const currentDate = new Date();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    return new Date(0, 0, 0, currentHours, currentMinutes);
  };

  const currentTime = getCurrentTime();

  const date = new Date(startDate);
  const today = new Date();

  const includesToday =
    date.getDate() <= today.getDate() &&
    date.getMonth() <= today.getMonth() &&
    date.getFullYear() <= today.getFullYear();

  const filterPassedTime = (time) => {
    if (!timeFrom) return true;
    const selectedTime = new Date(time);
    const minAllowedTime = getMinTime();
    return selectedTime >= minAllowedTime;
  };

  const minTime = includesToday ? currentTime : undefined;

  const getMinTime = () => {
    if (!timeFrom) return null;

    const timeFromObj = new Date(timeFrom);
    const tenMinutesLater = new Date(timeFromObj.getTime() + 10 * 60000); // 10 minutes in milliseconds

    return tenMinutesLater;
  };

  const setTimeField = (date, time) => {
    const value = new Date(formatDateToYYYYMMDD(date)); // Your time value
    value.setHours(time?.split(":")[0]); // Set the hours to 13 (1:00 PM)
    value.setMinutes(time?.split(":")[1]); // Set the minutes to 0

    // const endTime = timeValue.toString();
    return value;
  };

  const getCourseDetails = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(isCoach),
      };
      const response = await fetch(`/api/course_details`, requestOptions);

      const result = await response.json();
      if (!result.error) {
        // console.log(result, "course data");
        setCourse(result);
        setSelectedCategory(result.category);
        setIsEditable(false);
        setCourseName(result.courseName);
        setCampName(result.campName);
        setMaximumGroupSize(result.maxGroupSize);
        setCourseDescription(result.description);
        setCourseAmeneties(result.amenitiesProvided);
        if (result.images && result.images.length > 0 && result.images[0].url) {
          setImageUrl([...result.images]);
          setSelectedImages([...result.images]);
        } else {
          setImageUrl([]);
          setSelectedImages([]);
        }
        setClassType(result.classType);
        setEndDate(result.dates.endDate.split("T")[0]);
        setFees30(result?.fees?.fees30);
        setFees45(result?.fees?.fees45);
        setFees60(result?.fees?.fees60);
        setPrice(result?.fees?.feesCourse);
        setIsItCamp(result.camp);
        // Set the first proficiency level if multiple exist, otherwise set the single one
        if (result.proficiency && result.proficiency.length > 0) {
          setProficiencyLevel(result.proficiency[0]);
        }
        setStartDate(result?.dates?.startDate.split("T")[0]);
        setTimeFrom(
          setTimeField(result?.dates?.startDate, result.dates?.startTime)
        );
        setIsCustomImage(result?.customImage ? result.customImage : false);
        setSelectedDays(result.dates.days);
        setCancellationPolicy(result.cancellationPolicy);
        setCarryThings(result.whatYouHaveToBring);
        setCourseFacility(result.facility);
        setTimeTo(setTimeField(result?.dates?.endDate, result.dates?.endTime));
        setSessionType(
          result.sessionType == "group"
            ? sessionTypeOption[0]
            : sessionTypeOption[1]
        );
        setCategoryType(
          categories.filter((x) => x.name === result.category)[0] || {}
        );
        let lastDate = getLastDateOfCurrentYear();
        lastDate = new Date(lastDate);
        lastDate.setHours(0, 0, 0, 0);
        let resultEndDate = formatDateToYYYYMMDD(result.dates.endDate);
        let resultEndDateObj = new Date(resultEndDate);
        resultEndDateObj.setHours(0, 0, 0, 0);
        if (lastDate.getTime() === resultEndDateObj.getTime()) {
          setIsEnd(false);
        } else {
          setIsEnd(true);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (isCoach) {
      getCourseDetails();
    }
  }, [isCoach, categories, setCategories]);

  const getCategories = async () => {
    try {
      let response = await axios.get(`${API}/api/category`);
      // console.log(response?.data?.data, "cat");
      setCategories(response?.data?.data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCategories();
    getFacilities();
  }, [isCoach]);

  const handleSubmit = (e) => {
    e.preventDefault();
  };

  const handleFileChange = async (e) => {
    try {
      setImageLoading(true);
      const file = e.target.files[0];

      if (file && file.size > 10 * 1024 * 1024) {
        setShow2(true);
        setMessage("Please select a file less than 10 MB.");
        setTimeout(() => {
          setShow2(false);
        }, 3000);
        setImageLoading(false);

        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${API}/api/course/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            // Authorization: `Bearer ${myCookie}`,
          },
        }
      );

      const url = await response?.data?.url;

      setImageUrl([...imageUrl, { url: url }]);
      setSelectedImages([...selectedImages, { url: url }]);
      setImageLoading(false);
    } catch (error) {
      console.log(error);
      setImageLoading(false);
    }
  };

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = new Date(currentYear, 11, 31); // Set to December 31st of the current year
    return formatDateToYYYYMMDD(lastDate);
  }

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    setSelectedImages([...selectedImages, ...Array.from(files)]);
  };

  const deleteImageFiles = async (index, url) => {
    try {
      const updatedImages = [...selectedImages];
      updatedImages.splice(index, 1);

      if (isCustomImage) {
        const formData = new FormData();
        formData.append("url", url);

        const response = await axios.post(
          `${API}/api/course/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        const resp = response?.data;
        setSelectedImages(updatedImages);
        setImageUrl(updatedImages);
      } else {
        setSelectedImages(updatedImages);
        setImageUrl(updatedImages);
      }
      // console.log(resp);
    } catch (error) {
      console.log(error);
    }
  };

  const handleRadioChange = (e) => {
    // console.log(e.target.value, "ppp");
    const value = e.target.value === "yes";
    setIsItCamp(value);
  };

  const getTime = (time) => {
    const tempdate = new Date(time);

    const hours = tempdate.getHours(); // Get the hour component (0-23)
    const minutes = tempdate.getMinutes(); // Get the minute component (0-59)

    // console.log(hours, minutes);

    // Format the time as needed (e.g., to a string with leading zeros)
    const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;

    return formattedTime;
  };

  const checkAvailableSlots = async (value) => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let lastDateOfYear = getLastDateOfCurrentYear();

      const startDateTime = `${startDate}T${getTime(timeFrom)}:00`;
      const endDateTime = `${endDate ? endDate : lastDateOfYear}T${
        timeTo !== "" ? getTime(timeTo) : getTime(value)
      }:00`;

      let raw = JSON.stringify({
        dates: {
          startDate: startDateTime,
          endDate: endDateTime,
          startTime: getTime(timeFrom),
          endTime: timeTo !== "" ? getTime(timeTo) : getTime(value),
          days: selectedDays,
          courseId: isCoach ? isCoach : null,
        },
      });

      // console.log(raw, "rqee");
      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
      };

      const response = await fetch(`/api/check_slots`, requestOptions);

      const result = await response.json();

      setBookedSlots(result.conflictingDates);

      if (result.message == "Slots available") {
        return true;
      } else {
        setBtnIsLoading(false);
        setShow2(true);
        setMessage("Conflicting slots");
        setTimeout(() => {
          setShow2(false);
        }, 3000);
        return false;
      }
    } catch (error) {
      console.log(error);
      setBtnIsLoading(false);
      setShow2(true);
      setTimeout(() => {
        setShow2(false);
      }, 3000);
    }
  };

  function daysDifference(startDate, endDate, daysArray) {
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const start = new Date(startDate);
    const end = new Date(endDate ? endDate : getLastDateOfCurrentYear());

    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
      if (daysArray.includes(daysOfWeek[currentDate.getDay()])) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return count;
  }

  // ### Final Save ###
  const saveHandler = async (e) => {
    try {
      e.preventDefault();
      setBtnIsLoading(true);

      let lastDateOfYear = getLastDateOfCurrentYear();
      lastDateOfYear = formatDateToYYYYMMDD(lastDateOfYear);
      if (sessionType?.name === "Individual") {
        setMaximumGroupSize(1);
      }
      // if (!selectedImages.length > 0) {
      //   setShow2(true);
      //   setMessage("Plesae select atleast 1 image");
      //   setTimeout(() => {
      //     setShow2(false);
      //   }, 3000);
      //   setBtnIsLoading(false);
      //   return;
      // }
      if (
        courseName !== "" &&
        timeFrom !== "" &&
        timeTo !== "" &&
        (isItCamp ? campName !== "" : true) &&
        selectedDays.length > 0 &&
        (fees30 !== "" || fees45 !== "" || fees60 !== "  ")
      ) {
        const startDateTime = `${startDate}T${getTime(timeFrom)}:00`;
        const endDateTime = `${endDate ? endDate : lastDateOfYear}T${getTime(
          timeTo
        )}:00`;

        let isAvailable = await checkAvailableSlots();
        // console.log(isAvailable);
        const isDaysExist = await daysDifference(
          startDate,
          endDate,
          selectedDays
        );

        if (isDaysExist == 0) {
          setBtnIsLoading(false);
          setShow2(true);
          setMessage("Selected days are not in between the selected dates.");
          setSelectedDaysError(true);
          setTimeout(() => {
            setShow2(false);
          }, 3000);
          return;
        }

        if (!isAvailable) {
          setBtnIsLoading(false);
          setShow2(true);
          setMessage("Slots are already booked");
          setTimeout(() => {
            setShow2(false);
          }, 3000);
          return;
        }

        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        let raw = {
          courseName: `${courseName.replace(/\s+/g, " ").trim()}`,
          description: `${courseDescription}`,
          images:
            selectedImages.length <= 0
              ? [{ url: categoryType?.image }]
              : imageUrl,
          coach_id: coach._id || "",
          customImage: selectedImages.length <= 0 ? false : true,
          coachName: `${coach.firstName || ""} ${coach.lastName || ""}`.trim(),
          category: `${categoryType?.name}`,
          sessionType:
            classType == "course" ? `${sessionType?.value}` : "individual",
          classType: classType,
          camp: isItCamp,
          campName: `${campName.replace(/\s+/g, " ").trim()}`,
          fees: {
            fees:
              classType === "course"
                ? price
                : fees30
                ? fees30
                : fees45
                ? fees45
                : fees60,
            feesCourse: classType == "class" ? "" : price,
            fees30: classType == "class" ? fees30 : "",
            fees45: classType == "class" ? fees45 : "",
            fees60: classType == "class" ? fees60 : "",
          },
          maxGroupSize: maximumGroupSize !== "" ? maximumGroupSize : 1,
          amenitiesProvided: courseAmeneties,
          proficiency: proficiencyLevel ? [proficiencyLevel] : [],
          facility: courseFacility,
          coachEmail: coach.email,
          dates: {
            startDate: startDateTime,
            endDate: endDateTime,
            startTime: getTime(timeFrom),
            endTime: getTime(timeTo),
            days: selectedDays,
          },
          whatYouHaveToBring: carryThings,
          cancellationPolicy: cancellationPolicy,
        };

        let requestOptions;

        if (isCoach) {
          requestOptions = {
            method: "PATCH",
            headers: myHeaders,
            body: JSON.stringify({
              ...raw,
              courseId: isCoach,
              eventId: course?.eventId,
              category: selectedCategory,
            }),
          };
        } else {
          requestOptions = {
            method: "POST",
            headers: myHeaders,
            body: JSON.stringify(raw),
          };
        }
        const response = await fetch(`/api/create_course`, requestOptions);

        const result = await response.json();

        // console.log(result, "result");
        setBtnIsLoading(false);

        if (!result.error) {
          setBtnIsLoading(false);
          // setCourseAmenetiesError(false)
          setCourseDescriptionError(false);
          setShow(true);
          setCourseName("");
          setCourseNameError(false);
          setCampName("");
          setCampNameError(false);
          setMaximumGroupSize("");
          setMaximumGroupSizeError(false);
          setCourseDescription("");
          setCourseAmeneties("");
          setStartDateError(false);
          setEndDate("");
          setPriceError(false);
          setFees30("");
          setFees45("");
          setFees60("");
          setStartDate("");
          setTimeFrom("");
          setSelectedDaysError(false);
          setSelectedDays([]);
          setCancellationPolicy("");
          setCarryThings("");
          setCategoryType({});
          setCourseFacility({});
          setTimeTo("");
          setFees30Error(false);
          setFees45Error(false);
          setFees60Error(false);
          setSelectedImages([]);
          setCategoryError(false);
          setFacilityError(false);
          setTimeout(() => {
            setShow(false);
          }, 3000);
          router.push("/course/list");
        } else {
          console.log("error", result.error);
          setBtnIsLoading(false);
          setShow2(true);
          setTimeout(() => {
            setShow2(false);
          }, 3000);
        }
      } else {
        setShow2(true);
        setMessage("Please fill all the fields");
        setTimeout(() => {
          setShow2(false);
        }, 3000);
        if (courseName.length < 3) {
          setCourseNameError(true);
        }
        if (maximumGroupSize.length === 0 || parseInt(maximumGroupSize) < 1) {
          setMaximumGroupSizeError(true);
        }
        // if (courseAmeneties.length === 0 || courseAmeneties === "<p><br></p>") {
        //   setCourseAmenetiesError(true);
        // }
        if (price === "") {
          setPriceError(true);
        }
        if (fees30 == "" || fees45 !== "" || fees60 !== "") {
          setFees30Error(true);
        }
        if (fees45 == "" || fees30 !== "" || fees60 !== "") {
          setFees45Error(true);
        }
        if (fees60 === "" || fees45 !== "" || fees30 !== "") {
          setFees60Error(true);
        }
        if (timeFrom == "") {
          setTimeFromError(true);
        }
        if (selectedDays.length == 0) {
          setSelectedDaysError(true);
        }
        if (courseFacility.name == "" || !courseFacility?.name) {
          setFacilityError(true);
        }
        if (
          (categoryType.name == "" || !categoryType?.name) &&
          course?.category == ""
        ) {
          setCategoryError(true);
        }
        if (timeFrom == "") {
          setTimeToError(true);
        }
        if (courseDescription === "" || courseDescription === "<p><br></p>") {
          setCourseDescriptionError(true);
        }
        if (campName.length === 0 && isItCamp) {
          setCampNameError(true);
        }
        if (startDate === "") {
          setStartDateError(true);
        }
        setBtnIsLoading(false);
      }
    } catch (error) {
      console.log("error", error);
      setBtnIsLoading(false);
      setShow2(true);
      setTimeout(() => {
        setShow2(false);
      }, 3000);
    }
  };

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  return (
    <>
      <title>Course Create and Update</title>
      {!isEditable && (
        <button
          type="button"
          // disabled={checkEditable()}
          className="inline-flex mt-5 mr-8 float-right items-center gap-x-1.5 rounded-md bg-indigo-600 px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          onClick={() => {
            const checkEdit = checkEditable();
            if (checkEdit) {
              setShow2(true);
              setMessage(
                "Can not edit the course, Course start date passed or player enrolled in the course"
              );
            } else {
              setIsEditable(true);
            }
          }}
        >
          <PencilSquareIcon className="-ml-0.5 h-5 w-5" aria-hidden="true" />
          Edit Form
        </button>
      )}
      <div className="w-full lg:w-2/3 md:2/3 m-auto ">
        <form onSubmit={handleSubmit}>
          <div className="px-4 py-6 sm:p-8">
            <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 w-full">
              <div className="col-span-full">
                <label
                  htmlFor="course-name"
                  className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Training Schedule Name
                </label>
                <div className="">
                  <input
                    type="text"
                    name="course-name"
                    disabled={!isEditable}
                    id="course-name"
                    value={courseName}
                    autoComplete="off"
                    maxLength={50}
                    className={
                      courseNameError
                        ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                        : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                    }
                    placeholder="Enter course name"
                    onChange={(e) => {
                      setCourseName(e.target.value);
                      if (e.target.value !== "" && e.target.value.length >= 3) {
                        setCourseNameError(false);
                      } else {
                        setCourseNameError(true);
                      }
                    }}
                  />
                  {courseNameError && (
                    <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                      Please enter course name with min 3 and max 50 characters
                    </span>
                  )}
                </div>
              </div>
              {/* Description */}
              <div className="col-span-full ">
                <label
                  htmlFor="description"
                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Description
                </label>
                <div
                  className={
                    courseDescriptionError
                      ? "mt-2 bg-white min-h-10 rounded-sm border-2 border-rose-500 text-gray-900 shadow-sm"
                      : "mt-2 bg-white min-h-10 rounded-md border border-gray-300 text-gray-900 shadow-sm"
                  }
                >
                  <ReactQuill
                    value={courseDescription}
                    readOnly={!isEditable}
                    onChange={(value) => {
                      // console.log(value, "ppp");
                      setCourseDescription(value);
                      if (value === "" || value === "<p><br></p>") {
                        setCourseDescriptionError(true);
                      } else {
                        setCourseDescriptionError(false);
                      }
                    }}
                  />
                </div>
                {courseDescriptionError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please enter description
                  </span>
                )}
              </div>
              {/* Upload Image */}
              <div
                className="col-span-full bg-white p-3 rounded-sm"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400">
                  <label
                    htmlFor="imageInput"
                    className="cursor-pointer flex flex-col items-center"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {/* SVG icon */}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-14 h-14"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                      />
                    </svg>

                    <span className="mt-2 block text-sm text-gray-500">
                      <span className="font-semibold text-blue-500">
                        {imageLoading ? (
                          <div role="status">
                            <svg
                              aria-hidden="true"
                              className="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                              viewBox="0 0 100 101"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="currentColor"
                              />
                              <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentFill"
                              />
                            </svg>
                            <span className="sr-only">Loading...</span>
                          </div>
                        ) : (
                          "Upload a file"
                        )}
                      </span>{" "}
                      {!imageLoading && "or drag and drop "}
                      <br />
                      {!imageLoading && "PNG, JPG, GIF up to 10MB"}
                    </span>
                    <input
                      id="imageInput"
                      type="file"
                      disabled={
                        !isEditable || imageLoading || selectedImages.length > 0
                      }
                      accept="image/*"
                      // multiple
                      onChange={handleFileChange}
                      className="hidden"
                    />
                  </label>
                  <div>
                    {selectedImages.length > 0 && (
                      <div className="mt-4 grid grid-cols-3 gap-4">
                        {selectedImages.map((image, index) => (
                          <div key={`image_${index}`} className="relative">
                            <img
                              src={image.url}
                              alt={`Selected Image ${index}`}
                              className="w-full h-full object-cover rounded"
                            />
                            <button
                              disabled={!isEditable}
                              onClick={(e) => {
                                deleteImageFiles(index, image.url);
                              }}
                              className="absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                className="h-4 w-4 text-red-500"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {/* Specific / Independent Classes */}
              <div className="col-span-full bg-white px-2 py-2 rounded-sm">
                <div className="flex flex-col gap-8">
                  <div className="space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0 justify-around">
                    <div className="flex items-center">
                      <input
                        // id={notificationMethod.id}
                        name="notification-method"
                        type="radio"
                        disabled={isCoach}
                        onChange={(e) =>
                          e.target.checked && setClassType("class")
                        }
                        checked={classType === "class"}
                        className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                      />
                      <label
                        // htmlFor={notificationMethod.id}
                        className="ml-3 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                      >
                        {"Session"}
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        // id={notificationMethod.id}
                        name="notification-method"
                        disabled={isCoach}
                        type="radio"
                        onChange={(e) => {
                          e.target.checked && setClassType("course");
                          e.target.checked && setIsEnd(true);
                        }}
                        checked={classType === "course"}
                        // defaultChecked={!isCoach && classType == "course"}
                        className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                      />
                      <label
                        // htmlFor={notificationMethod.id}
                        className="ml-3 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                      >
                        {"Course"}
                      </label>
                    </div>
                  </div>

                  <div>
                    <div className="flex flex-col justify-items-center gap-8">
                      {/*date*/}
                      <div className="col-span-full">
                        <label
                          htmlFor="start-date"
                          className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                        >
                          Start Date
                        </label>
                        {coach?.academyAvailability?.startDate && coach?.academyAvailability?.endDate && (
                          <p className="text-sm text-gray-600 mb-2">
                            Available date range: {formatDateToYYYYMMDD(coach.academyAvailability.startDate)} to {formatDateToYYYYMMDD(coach.academyAvailability.endDate)}
                          </p>
                        )}
                        <div className="">
                          <input
                            type="date"
                            name="start-date"
                            id="start-date"
                            onKeyDown={(e) => e.preventDefault()}
                            value={startDate}
                            disabled={!isEditable}
                            min={(() => {
                              const coachDateRange = getCoachDateRange();
                              const today = formatDateToYYYYMMDD(new Date());

                              // Use the later of today or coach's start date
                              if (coachDateRange.minDate && today) {
                                return coachDateRange.minDate > today ? coachDateRange.minDate : today;
                              }
                              return coachDateRange.minDate || today;
                            })()}
                            max={(() => {
                              const coachDateRange = getCoachDateRange();
                              return coachDateRange.maxDate;
                            })()}
                            autoComplete="off"
                            className={
                              startDateError
                                ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                            }
                            // placeholder="Enter course name"
                            onChange={(e) => {
                              const selectedDate = e.target.value;
                              setStartDate(selectedDate);
                              setTimeFrom("");
                              setTimeTo("");

                              if (selectedDate !== "") {
                                // Validate if date is within coach's available range
                                if (isDateWithinCoachRange(selectedDate)) {
                                  setStartDateError(false);
                                } else {
                                  setStartDateError(true);
                                }
                              } else {
                                setStartDateError(true);
                              }
                            }}
                          />
                          {startDateError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              {startDate === ""
                                ? "Please select the start date"
                                : "Please select a date within your academy's available period"
                              }
                            </span>
                          )}
                        </div>
                      </div>
                      {/*days*/}
                      <div className="">
                        <label className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900">
                          Select the Days
                        </label>
                        {coach?.academyAvailability?.days && coach.academyAvailability.days.length > 0 && (
                          <p className="text-sm text-gray-600 mb-2">
                            Available days based on your academy schedule: {coach.academyAvailability.days.join(", ")}
                          </p>
                        )}
                        <div className="flex text-center text-xs leading-6 text-gray-500 gap-2 w-[95%]">
                          {daysOfWeek.map((day, dayIdx) => {
                            const isAvailable = isDayAvailable(day);
                            return (
                              <button
                                key={dayIdx}
                                type="button"
                                disabled={!isEditable || !isAvailable}
                                onClick={() => {
                                  if (isAvailable) {
                                    setSelectedDays(
                                      selectedDays.includes(day)
                                        ? selectedDays.filter(
                                            (selected) => selected !== day
                                          )
                                        : [...selectedDays, day]
                                    );
                                    if (!day) {
                                      setSelectedDaysError(true);
                                    } else {
                                      setSelectedDaysError(false);
                                    }
                                  }
                                }}
                                className={`${
                                  selectedDays.includes(day)
                                    ? "bg-blue-500 text-white w-[13%] flex-shrink-0 cursor-pointer"
                                    : isAvailable
                                    ? "w-[13%] flex-shrink-0 cursor-pointer"
                                    : "w-[13%] flex-shrink-0 cursor-not-allowed bg-gray-200 text-gray-400"
                                } p-2 rounded-md`}
                                title={!isAvailable ? "This day is not available in your academy schedule" : ""}
                              >
                                {day}
                              </button>
                            );
                          })}
                        </div>
                        {selectedDaysError && (
                          <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                            Select the Days
                          </span>
                        )}
                      </div>

                      {/* end date */}
                      {classType === "class" && (
                        <div className="">
                          <label
                            htmlFor="end_date"
                            className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize"
                          >
                            End Date:
                          </label>
                          <ul className="items-center w-full text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg sm:flex dark:border-gray-600 dark:text-white">
                            <li className="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                              <div className="flex items-center ps-3">
                                <input
                                  id="horizontal-list-radio-license"
                                  type="radio"
                                  required
                                  disabled={!isEditable}
                                  checked={!isEnd}
                                  // defaultChecked={!isEnd}
                                  onChange={(e) => {
                                    e.target.checked
                                      ? setIsEnd(false)
                                      : setIsEnd(true);
                                    setEndDate("");
                                  }}
                                  name="list-radio"
                                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300  dark:bg-gray-600 dark:border-gray-500"
                                />
                                <label
                                  htmlFor="horizontal-list-radio-license"
                                  className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                                >
                                  Never
                                </label>
                              </div>
                            </li>

                            <li className="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                              <div className="flex items-center ps-3">
                                <input
                                  id="horizontal-list-radio-license"
                                  type="radio"
                                  required
                                  disabled={!isEditable}
                                  value=""
                                  checked={isEnd}
                                  onChange={(e) => {
                                    e.target.checked
                                      ? setIsEnd(true)
                                      : setIsEnd(false);
                                  }}
                                  name="list-radio"
                                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300  dark:bg-gray-600 dark:border-gray-500"
                                />
                                <label
                                  htmlFor="horizontal-list-radio-license"
                                  className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                                >
                                  On
                                </label>
                              </div>
                            </li>
                          </ul>
                        </div>
                      )}

                      {isEnd && (
                        <div className="mt-3">
                          <label
                            htmlFor="start_date"
                            className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize"
                          >
                            End Date:
                          </label>
                          <input
                            type="date"
                            disabled={!isEditable || startDate == ""}
                            value={endDate}
                            onKeyDown={(e) => e.preventDefault()}
                            min={(() => {
                              const coachDateRange = getCoachDateRange();
                              const startDateFormatted = formatDateToYYYYMMDD(startDate);

                              // Use the later of start date or coach's start date
                              if (coachDateRange.minDate && startDateFormatted) {
                                return coachDateRange.minDate > startDateFormatted ? coachDateRange.minDate : startDateFormatted;
                              }
                              return startDateFormatted || coachDateRange.minDate;
                            })()}
                            max={(() => {
                              const coachDateRange = getCoachDateRange();
                              return coachDateRange.maxDate;
                            })()}
                            id="start_date"
                            onChange={(e) => {
                              const selectedEndDate = e.target.value;
                              setEndDate(selectedEndDate);

                              // Validate if end date is within coach's available range
                              if (selectedEndDate !== "" && !isDateWithinCoachRange(selectedEndDate)) {
                                // You could add an end date error state here if needed
                                console.warn("End date is outside coach's available range");
                              }
                            }}
                            required
                            className="mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                          />
                        </div>
                      )}

                      {/* time slots */}
                      {coach?.academyAvailability?.startTime && coach?.academyAvailability?.endTime && (
                        <div className="mb-4">
                          <p className="text-sm text-gray-600">
                            Available time based on your academy schedule: {coach.academyAvailability.startTime} - {coach.academyAvailability.endTime}
                          </p>
                        </div>
                      )}
                      <div className="flex flex-row justify-between">
                        <div>
                          <label className=" mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize">
                            Start Time:
                          </label>
                          <DatePicker
                            selected={timeFrom}
                            onKeyDown={(e) => e.preventDefault()}
                            className={
                              timeFromError
                                ? "my-custom-datepicker dateError"
                                : "my-custom-datepicker"
                            }
                            onChange={(value) => {
                              if (value == "") {
                                setTimeFromError(true);
                              } else {
                                setTimeFromError(false);
                              }
                              setTimeFrom(value);
                              setTimeTo("");
                            }}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={10}
                            timeCaption="Time"
                            dateFormat="h:mm aa"
                            popperPlacement="left-start"
                            placeholderText="Select time"
                            disabled={startDate == "" || !isEditable}
                            minTime={(() => {
                              const coachTimeRange = getCoachTimeRange();
                              const currentMinTime = minTime || new Date(0, 0, 0, 0, 0);

                              // Use the later of current time restrictions or coach's start time
                              if (coachTimeRange.minTime && currentMinTime) {
                                return coachTimeRange.minTime > currentMinTime ? coachTimeRange.minTime : currentMinTime;
                              }
                              return coachTimeRange.minTime || currentMinTime;
                            })()}
                            maxTime={(() => {
                              const coachTimeRange = getCoachTimeRange();
                              return coachTimeRange.maxTime || new Date(0, 0, 0, 23, 59);
                            })()}
                            // excludeTimes={excludedSlots || []}
                          />
                          <br />
                          {timeFromError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              Select the time
                            </span>
                          )}
                        </div>

                        <div>
                          <label className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize">
                            End Time:
                          </label>
                          <DatePicker
                            selected={timeTo}
                            onKeyDown={(e) => e.preventDefault()}
                            onChange={async (value) => {
                              // console.log(value, "timetotoo");
                              setTimeTo(value);
                              if (value == "") {
                                setTimeToError(true);
                              } else {
                                setTimeToError(false);
                              }
                              await checkAvailableSlots(value);
                            }}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={10}
                            timeCaption="Time"
                            dateFormat="h:mm aa"
                            className={
                              timeToError
                                ? "my-custom-datepicker dateError"
                                : "my-custom-datepicker"
                            }
                            popperPlacement="left-start"
                            placeholderText="Select time"
                            disabled={timeFrom === "" || !isEditable}
                            minTime={(() => {
                              const normalMinTime = getMinTime();
                              const coachTimeRange = getCoachTimeRange();

                              // Use the later of normal min time or coach's start time
                              if (coachTimeRange.minTime && normalMinTime) {
                                return coachTimeRange.minTime > normalMinTime ? coachTimeRange.minTime : normalMinTime;
                              }
                              return normalMinTime || coachTimeRange.minTime;
                            })()}
                            filterTime={isCoach ? "" : filterPassedTime}
                            maxTime={(() => {
                              const coachTimeRange = getCoachTimeRange();
                              return coachTimeRange.maxTime || new Date(0, 0, 0, 23, 59);
                            })()}
                            // excludeTimes={excludedSlots || []}
                          />
                          <br />
                          {timeToError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              Select the time
                            </span>
                          )}
                        </div>
                      </div>

                      {bookedSlots && bookedSlots.length > 0 && (
                        <div className="mt-8 flow-root">
                          <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                              <p className="text-red-500 text-center">
                                Conflicting Courses
                              </p>
                              <table className="min-w-full divide-y divide-red-500 border border-red-500">
                                <thead>
                                  <tr>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      Start Date
                                    </th>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      End Date
                                    </th>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      Start Time
                                    </th>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      End Time
                                    </th>
                                    <th
                                      scope="col"
                                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                    >
                                      Days
                                    </th>
                                  </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                  {bookedSlots.map((slot, idx) => (
                                    <tr key={idx}>
                                      <td className="whitespace-nowrap py-4 pl-6 pr-3 text-sm font-medium text-gray-900 sm:pl-4">
                                        {slot?.startDate &&
                                          new Date(
                                            slot?.startDate
                                          ).toDateString()}
                                      </td>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {slot?.endDate &&
                                          new Date(
                                            slot?.endDate
                                          ).toDateString()}
                                      </td>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {slot?.conflictingStartTime}
                                      </td>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {slot?.conflictingEndTime}
                                      </td>
                                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        <ul>
                                          {slot?.conflictingDays?.map(
                                            (days, i) => (
                                              <li key={i}>{days}</li>
                                            )
                                          )}
                                        </ul>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      )}
                      {/* <br /> */}

                      {/*Price*/}

                      {classType === "class" ? (
                        <div className="flex flex-row justify-between">
                          <div>
                            <label
                              htmlFor="start_date"
                              className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize"
                            >
                              Price:
                            </label>
                            30 Min
                            <input
                              type="number"
                              name="price"
                              id="price"
                              onFocus={(e) =>
                                e.target.addEventListener(
                                  "wheel",
                                  function (e) {
                                    e.preventDefault();
                                  },
                                  { passive: false }
                                )
                              }
                              placeholder="₹0.00"
                              value={fees30}
                              disabled={!isEditable}
                              autoComplete="off"
                              className={
                                fees30Error
                                  ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                  : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                              }
                              // placeholder="Enter course name"
                              onChange={(e) => {
                                const newValue = parseInt(e.target.value);
                                setFees30(newValue);
                                if (
                                  e.target.value === "" &&
                                  !(fees45 || fees60)
                                ) {
                                  setFees30Error(true);
                                } else {
                                  setFees60Error(false);
                                  setFees30Error(false);
                                  setFees45Error(false);
                                }
                              }}
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {fees30Error && (
                              <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                                Please enter the price
                              </span>
                            )}
                          </div>

                          {/* <div>
                            <label
                              htmlFor="start_date"
                              className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize"
                            >
                              Price:
                            </label>
                            45 Min
                            <input
                              type="number"
                              name="price"
                              id="price"
                              disabled={!isEditable}
                              placeholder="₹0.00"
                              value={fees45}
                              autoComplete="off"
                              onFocus={(e) =>
                                e.target.addEventListener(
                                  "wheel",
                                  function (e) {
                                    e.preventDefault();
                                  },
                                  { passive: false }
                                )
                              }
                              className={
                                fees45Error
                                  ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                  : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                              }
                              // placeholder="Enter course name"
                              onChange={(e) => {
                                const newValue = parseInt(e.target.value);
                                setFees45(newValue);
                                if (
                                  e.target.value === "" &&
                                  !(fees30 || fees60)
                                ) {
                                  setFees45Error(true);
                                } else {
                                  setFees60Error(false);
                                  setFees30Error(false);
                                  setFees45Error(false);
                                }
                              }}
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {fees45Error && (
                              <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                                Please enter the price
                              </span>
                            )}
                          </div> */}

                          <div>
                            <label
                              htmlFor="start_date"
                              className="block mb-2 text-[16px]font-medium text-gray-900 dark:text-black capitalize"
                            >
                              Price:
                            </label>
                            60 Min
                            <input
                              type="number"
                              name="price"
                              id="price"
                              disabled={!isEditable}
                              placeholder="₹0.00"
                              value={fees60}
                              autoComplete="off"
                              onFocus={(e) =>
                                e.target.addEventListener(
                                  "wheel",
                                  function (e) {
                                    e.preventDefault();
                                  },
                                  { passive: false }
                                )
                              }
                              className={
                                fees60Error
                                  ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                  : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                              }
                              // placeholder="Enter course name"
                              onChange={(e) => {
                                const newValue = parseInt(e.target.value);
                                setFees60(newValue);
                                if (
                                  e.target.value === "" &&
                                  !(fees30 || fees45)
                                ) {
                                  setFees60Error(true);
                                } else {
                                  setFees60Error(false);
                                  setFees30Error(false);
                                  setFees45Error(false);
                                }
                              }}
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {fees60Error && (
                              <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                                Please enter the price
                              </span>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="col-span- mt-3">
                          <label
                            htmlFor="price"
                            className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                          >
                            Price
                          </label>
                          <div className="">
                            <input
                              type="number"
                              name="price"
                              id="price"
                              placeholder="₹0.00"
                              disabled={!isEditable}
                              value={price}
                              autoComplete="off"
                              onFocus={(e) =>
                                e.target.addEventListener(
                                  "wheel",
                                  function (e) {
                                    e.preventDefault();
                                  },
                                  { passive: false }
                                )
                              }
                              className={
                                priceError
                                  ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                  : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                              }
                              // placeholder="Enter course name"
                              onChange={(e) => {
                                const newValue = parseInt(e.target.value);
                                setPrice(newValue);
                                if (e.target.value !== "") {
                                  setPriceError(false);
                                } else {
                                  setPriceError(true);
                                }
                              }}
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {priceError && (
                              <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                                Please enter the price
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              {classType === "course" && (
                <div className="col-span-full">
                  <Listbox
                    value={sessionType}
                    disabled={isCoach}
                    onChange={(e) => {
                      setSessionType(sessionTypeOption[e.id]);
                      setMaximumGroupSize(1);
                    }}
                  >
                    {({ open }) => (
                      <>
                        <Listbox.Label className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize">
                          Session Type
                        </Listbox.Label>
                        <div className="relative ">
                          <Listbox.Button className="relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6">
                            <span className="block truncate">
                              {sessionType.name}
                            </span>
                            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                              <ChevronUpDownIcon
                                className="h-5 w-5 text-gray-400"
                                aria-hidden="true"
                              />
                            </span>
                          </Listbox.Button>

                          <Transition
                            show={open}
                            as={Fragment}
                            leave="transition ease-in duration-100"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                          >
                            <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                              {sessionTypeOption.map((session, idx) => (
                                <Listbox.Option
                                  key={idx}
                                  className={({ active }) =>
                                    classNames(
                                      active
                                        ? "bg-indigo-600 text-white"
                                        : "text-gray-900",
                                      "relative cursor-default select-none py-2 pl-3 pr-9"
                                    )
                                  }
                                  value={session}
                                >
                                  {({ selected, active }) => (
                                    <>
                                      <span
                                        className={classNames(
                                          selected
                                            ? "font-semibold"
                                            : "font-normal",
                                          "block truncate"
                                        )}
                                      >
                                        {session.name}
                                      </span>

                                      {selected ? (
                                        <span
                                          className={classNames(
                                            active
                                              ? "text-white"
                                              : "text-indigo-600",
                                            "absolute inset-y-0 right-0 flex items-center pr-4"
                                          )}
                                        >
                                          <CheckIcon
                                            className="h-5 w-5"
                                            aria-hidden="true"
                                          />
                                        </span>
                                      ) : null}
                                    </>
                                  )}
                                </Listbox.Option>
                              ))}
                            </Listbox.Options>
                          </Transition>
                        </div>
                      </>
                    )}
                  </Listbox>
                </div>
              )}
              {/* ------ */}
              {/* Is it a camp */}
              {classType !== "class" && (
                <div className="col-span-full">
                  <div className="flex gap-4 items-center">
                    <label
                      htmlFor="camp-name"
                      className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                    >
                      Is it a camp?
                    </label>
                    <div className="flex items-center gap-1">
                      <input
                        type="radio"
                        id="yes"
                        name="camp-type"
                        checked={isItCamp}
                        disabled={!isEditable}
                        value="yes"
                        onChange={handleRadioChange}
                      />
                      <label htmlFor="yes" className="text-sm text-gray-700">
                        Yes
                      </label>
                    </div>
                    <div className="flex items-center gap-1">
                      <input
                        type="radio"
                        id="no"
                        disabled={!isEditable}
                        name="camp-type"
                        checked={!isItCamp}
                        value="no"
                        onChange={handleRadioChange}
                      />
                      <label htmlFor="no" className="text-sm text-gray-700">
                        No
                      </label>
                    </div>
                  </div>
                  {isItCamp && (
                    <div className="mt-2">
                      <input
                        type="text"
                        name="camp-name"
                        id="camp-name"
                        value={campName}
                        autoComplete="off"
                        disabled={!isEditable}
                        className={
                          campNameError
                            ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                            : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                        }
                        placeholder="Enter camp name"
                        onChange={(e) => {
                          setCampName(e.target.value);
                          if (e.target.value !== "") {
                            setCampNameError(false);
                          } else {
                            setCampNameError(true);
                          }
                        }}
                      />
                      {campNameError && (
                        <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                          Please enter camp name
                        </span>
                      )}
                    </div>
                  )}
                </div>
              )}
              {/* Category */}
              <div className="col-span-full">
                {!isCoach ? (
                  <Listbox
                    onChange={(e) => {
                      // console.log(e);
                      if (!e.name) {
                        setCategoryError(true);
                      } else {
                        setCategoryError(false);
                      }
                      setCategoryType(e);
                    }}
                    disabled={isCoach}
                  >
                    {({ open }) => (
                      <>
                        <Listbox.Label className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize">
                          Category
                        </Listbox.Label>
                        <div className="relative">
                          <Listbox.Button
                            className={
                              categoryError
                                ? " block w-full px-3 rounded-md border-2 border-rose-500  cursor-default bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6"
                                : "relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6"
                            }
                          >
                            <span className="block truncate">
                              {categoryType?.name
                                ? categoryType?.name
                                : "Select a category"}
                            </span>
                            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                              <ChevronUpDownIcon
                                className="h-5 w-5 text-gray-400"
                                aria-hidden="true"
                              />
                            </span>
                          </Listbox.Button>

                          <Transition
                            show={open}
                            as={Fragment}
                            leave="transition ease-in duration-100"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                          >
                            <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                              {categories &&
                                categories.length > 0 &&
                                categories.map((category, idx) => (
                                  <Listbox.Option
                                    key={idx}
                                    className={({ active }) =>
                                      classNames(
                                        active
                                          ? "bg-indigo-600 text-white"
                                          : "text-gray-900",
                                        "relative cursor-default select-none py-2 pl-3 pr-9"
                                      )
                                    }
                                    value={category}
                                  >
                                    {({ selected, active }) => (
                                      <>
                                        <span
                                          className={classNames(
                                            selected
                                              ? "font-semibold"
                                              : "font-normal",
                                            "block truncate"
                                          )}
                                        >
                                          {category.name}
                                        </span>

                                        {selected ? (
                                          <span
                                            className={classNames(
                                              active
                                                ? "text-white"
                                                : "text-indigo-600",
                                              "absolute inset-y-0 right-0 flex items-center pr-4"
                                            )}
                                          >
                                            <CheckIcon
                                              className="h-5 w-5"
                                              aria-hidden="true"
                                            />
                                          </span>
                                        ) : null}
                                      </>
                                    )}
                                  </Listbox.Option>
                                ))}
                            </Listbox.Options>
                          </Transition>
                        </div>
                      </>
                    )}
                  </Listbox>
                ) : (
                  <div>
                    <label
                      htmlFor="price"
                      className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                    >
                      Category
                    </label>
                    <div className="">
                      <input
                        type="text"
                        name="category"
                        id="category"
                        // placeholder="₹0.00"
                        disabled
                        value={course.category}
                        autoComplete="off"
                        className="mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                        // placeholder="Enter course name"
                        // onChange={(e) => {
                        //   setPrice(e.target.value);
                        //   if (e.target.value !== "") {
                        //     setPriceError(false);
                        //   } else {
                        //     setPriceError(true);
                        //   }
                        // }}
                      />
                      {/* <style jsx>{`
                        input[type="number"]::-webkit-inner-spin-button,
                        input[type="number"]::-webkit-outer-spin-button {
                          -webkit-appearance: none;
                          margin: 0;
                        }

                        input[type="number"] {
                          -moz-appearance: textfield;
                        }
                      `}</style> */}
                    </div>
                  </div>
                )}
                {categoryError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please select the category
                  </span>
                )}
              </div>

              {/* ----- */}
              {/* Select Facility */}
              <div className="col-span-full">
                <Listbox
                  value={courseFacility}
                  disabled={!isEditable}
                  onChange={(e) => {
                    if (!e.name) {
                      setFacilityError(true);
                    } else {
                      setFacilityError(false);
                    }
                    // console.log(e);
                    setCourseFacility(e);
                    setCourseAmeneties(e.amenities);
                  }}
                >
                  {({ open }) => (
                    <>
                      <Listbox.Label className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize">
                        Select Facility
                      </Listbox.Label>
                      <div className="relative">
                        <Listbox.Button
                          className={
                            facilityError
                              ? "mt-1 block w-full px-3 rounded-md border-2 border-rose-500  cursor-default bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6"
                              : "relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6"
                          }
                        >
                          <span className="block truncate">
                            {courseFacility.name
                              ? courseFacility.name
                              : "Select the facility"}
                          </span>
                          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon
                              className="h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                          </span>
                        </Listbox.Button>

                        <Transition
                          show={open}
                          as={Fragment}
                          leave="transition ease-in duration-100"
                          leaveFrom="opacity-100"
                          leaveTo="opacity-0"
                        >
                          <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                            {facilities.map((facility, idx) => (
                              <Listbox.Option
                                key={idx}
                                className={({ active }) =>
                                  classNames(
                                    active
                                      ? "bg-indigo-600 text-white"
                                      : "text-gray-900",
                                    "relative cursor-default select-none py-2 pl-3 pr-9"
                                  )
                                }
                                value={facility}
                              >
                                {({ selected, active }) => (
                                  <>
                                    <span
                                      className={classNames(
                                        selected
                                          ? "font-semibold"
                                          : "font-normal",
                                        "block truncate"
                                      )}
                                    >
                                      {facility.name}
                                    </span>

                                    {selected ? (
                                      <span
                                        className={classNames(
                                          active
                                            ? "text-white"
                                            : "text-indigo-600",
                                          "absolute inset-y-0 right-0 flex items-center pr-4"
                                        )}
                                      >
                                        <CheckIcon
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </span>
                                    ) : null}
                                  </>
                                )}
                              </Listbox.Option>
                            ))}
                          </Listbox.Options>
                        </Transition>
                      </div>
                    </>
                  )}
                </Listbox>
                {facilityError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please select the facility
                  </span>
                )}
              </div>
              {/* Maximum Group Size */}
              {sessionType?.name === "Group" && classType == "course" && (
                <div className="col-span-full">
                  <label
                    htmlFor="max-group-size"
                    className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                  >
                    Maximum Group Size
                  </label>
                  <div className="">
                    <input
                      type="number"
                      disabled={!isEditable}
                      name="max-group-size"
                      id="max-group-size"
                      value={maximumGroupSize}
                      min="1"
                      onFocus={(e) =>
                        e.target.addEventListener(
                          "wheel",
                          function (e) {
                            e.preventDefault();
                          },
                          { passive: false }
                        )
                      }
                      className={
                        maximumGroupSizeError
                          ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                          : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                      }
                      placeholder="Enter max group size"
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        // Prevent values less than 1
                        if (value < 1 && e.target.value !== "") {
                          setMaximumGroupSize(1);
                          setMaximumGroupSizeError(false);
                        } else {
                          setMaximumGroupSize(e.target.value);
                          if (e.target.value !== "" && value >= 1) {
                            setMaximumGroupSizeError(false);
                          } else {
                            setMaximumGroupSizeError(true);
                          }
                        }
                      }}
                    />
                    <style jsx>{`
                      input[type="number"]::-webkit-inner-spin-button,
                      input[type="number"]::-webkit-outer-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                      }

                      input[type="number"] {
                        -moz-appearance: textfield;
                      }
                    `}</style>
                    {maximumGroupSizeError && (
                      <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                        Please enter a valid group size (minimum 1)
                      </span>
                    )}
                  </div>
                </div>
              )}
              {/* Proficiency Level */}
              <div className="col-span-full">
                <label
                  htmlFor="proficiency"
                  className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Proficiency Level
                </label>
                <select
                  name="proficiency"
                  id="proficiency"
                  disabled={!isEditable}
                  value={proficiencyLevel}
                  onChange={(e) => {
                    setProficiencyLevel(e.target.value);
                  }}
                  className="mt-1 block w-full px-3 py-2 rounded-md border-0 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                >
                  <option value="">Select Proficiency Level</option>
                  {proficiencyLevelOption.map((option) => (
                    <option key={option.id} value={option.value}>
                      {option.name}
                    </option>
                  ))}
                </select>


              </div>
              {/* Ameneties */}
              <div className="col-span-full ">
                <label
                  htmlFor="description"
                  className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Ameneties
                </label>
                <div
                  className={
                    // courseAmenetiesError
                    //   ? "mt-2 bg-white min-h-10 rounded-sm border-2 border-rose-500 text-gray-900 shadow-sm":
                    " bg-white min-h-10 rounded-md border border-gray-300 text-gray-900 shadow-sm"
                  }
                >
                  <ReactQuill
                    value={courseAmeneties}
                    readOnly={!isEditable}
                    onChange={(value) => {
                      setCourseAmeneties(value);
                    }}
                  />
                </div>
                {/* {courseAmenetiesError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please enter ameneties
                  </span>
                )} */}
              </div>
              {/*whatyouhavetobring*/}
              <div className="col-span-full ">
                <label
                  htmlFor="carryThings"
                  className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Thing Have to Carry with
                </label>
                <div
                  className={
                    // courseDescriptionError
                    //   ? "mt-2 bg-white min-h-10 rounded-sm border-2 border-rose-500 text-gray-900 shadow-sm" :
                    "bg-white min-h-10 rounded-md border border-gray-300 text-gray-900 shadow-sm"
                  }
                >
                  <ReactQuill
                    value={carryThings}
                    readOnly={!isEditable}
                    onChange={(value) => {
                      setCarryThings(value);
                    }}
                  />
                </div>
                {/* {courseDescriptionError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please enter description
                  </span>
                )} */}
              </div>
              {/*cancellationPolicy*/}
              <div className="col-span-full ">
                <label
                  htmlFor="cancellationPolicy"
                  className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                >
                  Cancellation Policy
                </label>
                <div
                  className={
                    // courseDescriptionError
                    //   ? "mt-2 bg-white min-h-10 rounded-sm border-2 border-rose-500 text-gray-900 shadow-sm":
                    " bg-white min-h-10 rounded-md border border-gray-300 text-gray-900 shadow-sm"
                  }
                >
                  <ReactQuill
                    value={cancellationPolicy}
                    readOnly={!isEditable}
                    onChange={(value) => {
                      setCancellationPolicy(value);
                    }}
                  />
                </div>
                {/* {courseDescriptionError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please enter description
                  </span>
                )} */}
              </div>
            </div>
          </div>
          {isEditable ? (
            <div className="flex items-center justify-end gap-x-6 border-t border-gray-900/10 px-4 py-4 sm:px-8">
              <button
                type="button"
                onClick={() => router.push("/calendar")}
                className=" rounded-md bg-black px-6 py-2.5 text-sm  text-white shadow-sm hover:bg-slate-950 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-slate-950"
              >
                Cancel
              </button>
              {btnIsLoading ? (
                <button
                  disabled
                  type="button"
                  className="text-white px-3 py-2.5 bg-indigo-700 hover:bg-blue-800   font-medium rounded text-sm text-center mr-2 dark:bg-indigo-600 dark:hover:bg-indigo-700  inline-flex items-center"
                >
                  <svg
                    aria-hidden="true"
                    role="status"
                    class="inline mr-3 w-4 h-4 text-white animate-spin"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                      fill="#E5E7EB"
                    ></path>
                    <path
                      d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                      fill="currentColor"
                    ></path>
                  </svg>
                  Loading...
                </button>
              ) : (
                <button
                  disabled={!isEditable}
                  type="submit"
                  className="rounded-md bg-indigo-600 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  onClick={saveHandler}
                >
                  Save
                </button>
              )}
            </div>
          ) : null}
        </form>

        {/* Notification on successfully Course Creation */}
        <div
          aria-live="assertive"
          className="pointer-events-none fixed inset-0 flex items-center justify-center px-4 py-6 sm:items-start sm:p-6 z-50"
        >
          <div className="flex w-full flex-col items-center space-y-4 sm:items-center fixed top-[70px]">
            {/* Notification panel, dynamically insert this into the live region when it needs to be displayed */}
            <Transition
              show={show}
              as={Fragment}
              enter="transform ease-out duration-300 transition"
              enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
              enterTo="translate-y-0 opacity-100 sm:translate-x-0"
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5">
                <div className="p-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <CheckCircleIcon
                        className="h-6 w-6 text-green-400"
                        aria-hidden="true"
                      />
                    </div>
                    <div className="ml-3 w-0 flex-1 pt-0.5">
                      <p className="text-sm font-medium text-gray-900">
                        Course Created Successfully
                      </p>
                    </div>
                    <div className="ml-4 flex flex-shrink-0">
                      <div
                        className="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        onClick={() => {
                          setShow(false);
                        }}
                      >
                        <span className="sr-only">Close</span>
                        <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Transition>
          </div>
        </div>

        {/* Notification on Error while Course Creation */}
        <div
          aria-live="assertive"
          className="pointer-events-none  inset-0 flex items-center justify-center px-4 py-6 sm:items-start sm:p-6 z-50 top-0"
        >
          <div className="flex w-full flex-col items-center space-y-4 sm:items-center fixed top-[70px]">
            {/* Notification panel, dynamically insert this into the live region when it needs to be displayed */}
            <Transition
              show={show2}
              as={Fragment}
              enter="transform ease-out duration-300 transition"
              enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
              enterTo="translate-y-0 opacity-100 sm:translate-x-0"
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <XCircleIcon
                      className="h-5 w-5 text-red-400"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      {message}
                    </h3>
                  </div>
                </div>
              </div>
            </Transition>
          </div>
        </div>
      </div>
    </>
  );
}
